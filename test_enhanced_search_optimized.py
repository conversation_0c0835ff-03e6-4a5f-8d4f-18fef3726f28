#!/usr/bin/env python3
"""
优化版增强搜索功能测试
基于测试结果进行优化
"""

import sys
import os

# 添加模块路径
sys.path.append('modules')

from modules.search_detail import SearchDetailModule

def test_mapping_effectiveness():
    """测试映射有效性 - 识别哪些映射是有效的"""
    print("🎯 测试映射有效性")
    print("=" * 50)

    search_detail = SearchDetailModule()

    # 测试已知的多重映射案例
    test_cases = {
        "MILK-251": {
            "expected_mappings": ["h_1240milk00251", "milk00251"],
            "expected_valid": ["h_1240milk00251"],  # 基于测试结果
            "expected_invalid": ["milk00251"]
        },
        "CAWD-797": {
            "expected_mappings": ["cawd00797"],
            "expected_valid": ["cawd00797"],
            "expected_invalid": []
        }
    }

    for code, info in test_cases.items():
        print(f"\n🧪 测试番号: {code}")
        print("-" * 30)
        
        # 生成映射
        possible_cids = search_detail._generate_possible_cids(code)
        print(f"🎯 生成的映射: {possible_cids}")
        
        # 验证映射
        valid_mappings = []
        invalid_mappings = []
        
        for cid in possible_cids:
            # 快速检查数据库
            try:
                from dmm_firefox_crawler import DMMFirefoxCrawler
                crawler = DMMFirefoxCrawler()
                
                import sqlite3
                if os.path.exists(crawler.db_file):
                    conn = sqlite3.connect(crawler.db_file)
                    cursor = conn.cursor()
                    cursor.execute("SELECT COUNT(*) FROM dmm_works WHERE cid = ?", (cid,))
                    count = cursor.fetchone()[0]
                    conn.close()
                    
                    if count > 0:
                        valid_mappings.append(cid)
                        print(f"   ✅ 有效映射: {cid} (数据库)")
                    else:
                        # 简单的HTTP HEAD请求检查（不下载内容）
                        import requests
                        url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={cid}/"
                        try:
                            response = requests.head(url, timeout=5)
                            if response.status_code == 200:
                                valid_mappings.append(cid)
                                print(f"   ✅ 有效映射: {cid} (在线验证)")
                            else:
                                invalid_mappings.append(cid)
                                print(f"   ❌ 无效映射: {cid} (状态码: {response.status_code})")
                        except:
                            invalid_mappings.append(cid)
                            print(f"   ❌ 无效映射: {cid} (网络错误)")
                            
            except Exception as e:
                print(f"   ⚠️ 检查映射时出错: {e}")
        
        # 对比预期结果
        print(f"\n📊 映射有效性分析:")
        print(f"   预期有效: {info['expected_valid']}")
        print(f"   实际有效: {valid_mappings}")
        print(f"   预期无效: {info['expected_invalid']}")
        print(f"   实际无效: {invalid_mappings}")
        
        # 准确率计算
        expected_valid_set = set(info['expected_valid'])
        actual_valid_set = set(valid_mappings)
        accuracy = len(expected_valid_set & actual_valid_set) / len(expected_valid_set) if expected_valid_set else 1.0
        print(f"   🎯 预测准确率: {accuracy:.2%}")

def test_optimized_search():
    """测试优化后的搜索流程"""
    print("\n\n🚀 测试优化搜索流程")
    print("=" * 50)

    search_detail = SearchDetailModule()

    test_queries = [
        "CAWD-797",  # 单一映射，应该快速成功
        "MILK-251",  # 多重映射，应该返回有效的那个
        "PRED-123",  # 需要实时爬取
        "NONEXIST-999"  # 不存在的番号
    ]

    for query in test_queries:
        print(f"\n🧪 优化搜索: {query}")
        print("-" * 30)
        
        import time
        start_time = time.time()
        
        result = search_detail.search_dmm_enhanced(query)
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result and result.get("success"):
            print(f"✅ 搜索成功 (耗时: {duration:.2f}秒)")
            print(f"📍 CID: {result.get('cid', 'N/A')}")
            print(f"📊 来源: {result.get('source', 'N/A')}")
            
            # 检查是否是多重映射结果
            if 'multiple_mappings' in result:
                print(f"🔀 多重映射: {len(result['multiple_mappings'])} 个选项")
            
        else:
            print(f"❌ 搜索失败 (耗时: {duration:.2f}秒)")
            print(f"💬 原因: {result.get('message', 'N/A') if result else '无结果'}")

def test_performance_comparison():
    """性能对比测试"""
    print("\n\n⚡ 性能对比测试")
    print("=" * 50)

    search_detail = SearchDetailModule()
    
    # 测试不同搜索模式的性能
    test_code = "CAWD-797"
    
    import time
    
    # 1. 普通搜索（使用缓存）
    print("1️⃣ 普通搜索（使用缓存）:")
    start_time = time.time()
    result1 = search_detail.search_dmm_enhanced(test_code, force_crawl=False)
    duration1 = time.time() - start_time
    print(f"   耗时: {duration1:.2f}秒")
    print(f"   结果: {'成功' if result1.get('success') else '失败'}")
    
    # 2. 强制爬取
    print("\n2️⃣ 强制爬取:")
    start_time = time.time()
    result2 = search_detail.search_dmm_enhanced(test_code, force_crawl=True)
    duration2 = time.time() - start_time
    print(f"   耗时: {duration2:.2f}秒")
    print(f"   结果: {'成功' if result2.get('success') else '失败'}")
    
    # 性能对比
    if duration1 > 0:
        speedup = duration2 / duration1
        print(f"\n📊 性能对比:")
        print(f"   缓存搜索比强制爬取快 {speedup:.1f} 倍")

def test_error_handling():
    """错误处理测试"""
    print("\n\n🛡️ 错误处理测试")
    print("=" * 50)

    search_detail = SearchDetailModule()
    
    error_test_cases = [
        ("", "空字符串"),
        ("INVALID", "无效格式"),
        ("TEST-99999", "不存在的番号"),
        ("MILK-999999", "超大数字"),
        ("123-ABC", "数字开头"),
    ]
    
    for test_input, description in error_test_cases:
        print(f"\n🧪 测试: {description} ('{test_input}')")
        
        try:
            result = search_detail.search_dmm_enhanced(test_input)
            if result and result.get("success"):
                print(f"   ⚠️ 意外成功: {result.get('message', '')}")
            else:
                print(f"   ✅ 正确处理: {result.get('message', '无结果') if result else '无结果'}")
        except Exception as e:
            print(f"   ❌ 异常未捕获: {e}")

def main():
    """主测试函数"""
    print("🧪 优化版增强搜索功能测试")
    print("=" * 60)
    
    # 检查依赖
    try:
        import requests
        import bs4
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        return
    
    print("\n" + "=" * 60)
    
    try:
        # 运行优化测试
        test_mapping_effectiveness()      # 映射有效性测试
        test_optimized_search()          # 优化搜索测试
        test_performance_comparison()    # 性能对比
        test_error_handling()           # 错误处理测试

        print("\n" + "=" * 60)
        print("✅ 优化测试完成！")
        
        print("\n💡 优化建议:")
        print("1. 建立映射有效性缓存，避免重复检查无效映射")
        print("2. 对多重映射结果进行智能排序，优先返回有效映射")
        print("3. 实现快速失败机制，减少无效请求的重试次数")
        print("4. 添加映射学习功能，自动标记无效映射")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
