#!/usr/bin/env python3
"""
集成爬虫的搜索服务
支持实时爬取DMM详情页信息
"""

import sqlite3
import re
import os
import time
import requests
from typing import Optional, Dict, List
from bs4 import BeautifulSoup
from datetime import datetime

class CrawlerIntegratedSearch:
    """集成爬虫的搜索服务"""
    
    def __init__(self, 
                 dmm_firefox_db: str = "dmm_firefox_database.db",
                 enable_realtime_crawl: bool = True):
        self.dmm_firefox_db = dmm_firefox_db
        self.enable_realtime_crawl = enable_realtime_crawl
        
        # 爬虫配置
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Cookie': 'age_check_done=1; cklg=ja'
        }
        
        print(f"🔍 爬虫集成搜索服务已启动")
        print(f"📊 实时爬取: {'✅ 启用' if enable_realtime_crawl else '❌ 禁用'}")
    
    def search_with_crawl(self, query: str, force_crawl: bool = False) -> Dict:
        """
        带爬虫功能的搜索
        
        Args:
            query: 搜索查询（番号或CID）
            force_crawl: 是否强制重新爬取
        
        Returns:
            搜索结果（包含完整详情）
        """
        print(f"🔍 搜索查询: {query}")
        
        # 1. 如果不强制爬取，先检查本地数据库
        if not force_crawl:
            local_result = self._search_local_database(query)
            if local_result and local_result.get("success"):
                print("✅ 在本地数据库中找到")
                return local_result
        
        # 2. 本地没有数据或强制爬取，启动实时爬虫
        if self.enable_realtime_crawl:
            print("🕷️ 启动实时爬虫...")
            crawl_result = self._crawl_dmm_details(query)
            if crawl_result and crawl_result.get("success"):
                print("✅ 实时爬取成功")
                return crawl_result
        
        # 3. 爬取失败，返回错误
        return {
            "success": False,
            "query": query,
            "message": "❌ 未找到相关信息",
            "source": "none"
        }
    
    def manual_search_and_crawl(self, query: str) -> Dict:
        """
        手动搜索并爬取（强制实时爬取）
        """
        print(f"🔍 手动搜索: {query}")
        return self.search_with_crawl(query, force_crawl=True)
    
    def batch_search_with_crawl(self, queries: List[str], max_crawl: int = 10) -> Dict[str, Dict]:
        """
        批量搜索（限制爬取数量避免被封）
        
        Args:
            queries: 查询列表
            max_crawl: 最大爬取数量
        
        Returns:
            批量搜索结果
        """
        results = {}
        crawl_count = 0
        
        for query in queries:
            print(f"\n🔍 批量搜索 {len(results)+1}/{len(queries)}: {query}")
            
            # 先尝试本地搜索
            local_result = self._search_local_database(query)
            if local_result and local_result.get("success"):
                results[query] = local_result
                continue
            
            # 如果本地没有且未达到爬取限制，进行爬取
            if crawl_count < max_crawl and self.enable_realtime_crawl:
                crawl_result = self._crawl_dmm_details(query)
                if crawl_result and crawl_result.get("success"):
                    results[query] = crawl_result
                    crawl_count += 1
                    # 爬取间隔
                    time.sleep(2)
                else:
                    results[query] = {
                        "success": False,
                        "query": query,
                        "message": "爬取失败"
                    }
            else:
                results[query] = {
                    "success": False,
                    "query": query,
                    "message": "未找到且已达爬取限制"
                }
        
        return results
    
    def _search_local_database(self, query: str) -> Optional[Dict]:
        """在本地数据库中搜索"""
        if not os.path.exists(self.dmm_firefox_db):
            return None
        
        try:
            conn = sqlite3.connect(self.dmm_firefox_db)
            cursor = conn.cursor()
            
            # 标准化查询
            normalized_query = query.upper().replace(" ", "").replace("-", "")
            
            # 多种搜索策略
            search_queries = [
                # 按CID搜索
                ("SELECT * FROM dmm_works WHERE cid = ? LIMIT 1", (query.lower(),)),
                # 按番号搜索
                ("SELECT * FROM dmm_works WHERE number = ? LIMIT 1", (query.upper(),)),
                # 模糊搜索
                ("SELECT * FROM dmm_works WHERE UPPER(REPLACE(REPLACE(number, '-', ''), ' ', '')) = ? LIMIT 1", (normalized_query,)),
                # 标题搜索
                ("SELECT * FROM dmm_works WHERE title LIKE ? LIMIT 1", (f"%{query}%",))
            ]
            
            for sql, params in search_queries:
                cursor.execute(sql, params)
                row = cursor.fetchone()
                if row:
                    conn.close()
                    return self._format_database_result(row, "local_database")
            
            conn.close()
            return None
            
        except Exception as e:
            print(f"❌ 本地数据库搜索失败: {e}")
            return None
    
    def _crawl_dmm_details(self, query: str) -> Optional[Dict]:
        """实时爬取DMM详情"""
        try:
            # 1. 构建可能的CID列表
            possible_cids = self._generate_possible_cids(query)
            
            for cid in possible_cids:
                print(f"🕷️ 尝试爬取CID: {cid}")
                
                # 构建详情页URL
                detail_url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={cid}/"
                
                # 爬取详情页
                detail_info = self._extract_detail_info(detail_url, cid)
                
                if detail_info:
                    # 保存到数据库
                    self._save_to_database(detail_info)
                    return detail_info
                
                # 爬取间隔
                time.sleep(1)
            
            return None
            
        except Exception as e:
            print(f"❌ 实时爬取失败: {e}")
            return None
    
    def _generate_possible_cids(self, query: str) -> List[str]:
        """生成可能的CID列表"""
        cids = []
        
        # 如果已经是CID格式
        if re.match(r'^[a-z]+\d+$', query.lower()):
            cids.append(query.lower())
        
        # 如果是番号格式，转换为CID
        if re.match(r'^[A-Z]+-\d+$', query.upper()):
            # 移除连字符，转小写
            cid = query.replace("-", "").lower()
            cids.append(cid)
            
            # 尝试添加前缀0
            match = re.match(r'^([a-z]+)(\d+)$', cid)
            if match:
                prefix, number = match.groups()
                # 尝试不同的数字格式
                for num_format in [f"{int(number):05d}", f"{int(number):04d}", f"{int(number):03d}"]:
                    formatted_cid = f"{prefix}{num_format}"
                    if formatted_cid not in cids:
                        cids.append(formatted_cid)
        
        return cids[:5]  # 限制尝试数量
    
    def _extract_detail_info(self, detail_url: str, cid: str) -> Optional[Dict]:
        """提取详情页信息（复用爬虫逻辑）"""
        try:
            print(f"🔍 访问详情页: {detail_url}")
            
            response = requests.get(detail_url, headers=self.headers, timeout=30)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # 检查是否为有效页面
                if "ページが見つかりません" in response.text:
                    return None
                
                detail_info = {
                    "success": True,
                    "source": "realtime_crawl",
                    "cid": cid,
                    "url": detail_url,
                    "crawl_time": datetime.now().isoformat()
                }
                
                # 提取标题
                title = self._extract_title(soup)
                if title:
                    detail_info['title'] = title
                
                # 提取其他字段（复用现有爬虫逻辑）
                fields_mapping = {
                    'release_date': ['配信開始日', '配信日'],
                    'sale_date': ['商品発売日', '発売日'],
                    'duration': ['収録時間', '時間'],
                    'actress': ['出演者'],
                    'director': ['監督'],
                    'series': ['シリーズ'],
                    'maker': ['メーカー'],
                    'label': ['レーベル']
                }
                
                for field, keywords in fields_mapping.items():
                    value = self._extract_field(soup, keywords)
                    if value:
                        detail_info[field] = value
                
                # 提取类型
                genre = self._extract_genre(soup)
                if genre:
                    detail_info['genre'] = genre
                
                # 提取评分
                rating = self._extract_rating(soup)
                if rating:
                    detail_info['rating'] = rating
                
                # 生成番号
                number = self._generate_number_from_cid(cid)
                if number:
                    detail_info['number'] = number
                    # 提取厂商
                    studio_match = re.match(r'^([A-Z]+)', number)
                    if studio_match:
                        detail_info['studio'] = studio_match.group(1)
                
                print(f"✅ 提取到 {len(detail_info)} 个字段")
                return detail_info
            
            return None
            
        except Exception as e:
            print(f"❌ 详情页提取失败: {e}")
            return None
    
    def _extract_title(self, soup: BeautifulSoup) -> Optional[str]:
        """提取标题（复用爬虫逻辑）"""
        try:
            # 从页面标题提取
            title_tag = soup.find('title')
            if title_tag:
                title_text = title_tag.get_text().strip()
                # 移除网站后缀
                title = re.sub(r'\s*-\s*(エロ動画・アダルトビデオ\s*-\s*)?FANZA動画.*$', '', title_text)
                if title and title != title_text:
                    return title.strip()
            
            # 从H1标签提取
            h1_tag = soup.find('h1')
            if h1_tag:
                return h1_tag.get_text().strip()
            
            return None
        except:
            return None
    
    def _extract_field(self, soup: BeautifulSoup, keywords: List[str]) -> Optional[str]:
        """提取字段信息"""
        try:
            for keyword in keywords:
                # 查找包含关键词的td标签
                td_tags = soup.find_all('td', string=re.compile(keyword))
                for td in td_tags:
                    next_td = td.find_next_sibling('td')
                    if next_td:
                        text = next_td.get_text().strip()
                        if text and text != '----':
                            return text
            return None
        except:
            return None
    
    def _extract_genre(self, soup: BeautifulSoup) -> Optional[str]:
        """提取类型"""
        try:
            genre_links = soup.find_all('a', href=re.compile(r'/digital/videoa/-/list/=/article=keyword/'))
            if genre_links:
                genres = [link.get_text().strip() for link in genre_links]
                return ', '.join(genres)
            return None
        except:
            return None
    
    def _extract_rating(self, soup: BeautifulSoup) -> Optional[str]:
        """提取评分"""
        try:
            # 查找评分相关的元素
            rating_elements = soup.find_all(string=re.compile(r'\d+\.\d+'))
            for element in rating_elements:
                if re.match(r'^\d+\.\d+$', element.strip()):
                    return element.strip()
            return None
        except:
            return None
    
    def _generate_number_from_cid(self, cid: str) -> Optional[str]:
        """从CID生成番号"""
        try:
            match = re.match(r'^([a-z]+)(\d+)$', cid.lower())
            if match:
                prefix, number = match.groups()
                return f"{prefix.upper()}-{int(number)}"
            return None
        except:
            return None
    
    def _save_to_database(self, detail_info: Dict):
        """保存到数据库"""
        try:
            conn = sqlite3.connect(self.dmm_firefox_db)
            cursor = conn.cursor()
            
            # 创建表（如果不存在）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dmm_works (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cid TEXT UNIQUE NOT NULL,
                    title TEXT,
                    number TEXT,
                    studio TEXT,
                    detail_url TEXT,
                    page_number INTEGER,
                    crawl_time TEXT,
                    release_date TEXT,
                    sale_date TEXT,
                    duration TEXT,
                    actress TEXT,
                    director TEXT,
                    series TEXT,
                    maker TEXT,
                    label TEXT,
                    genre TEXT,
                    rating TEXT,
                    detail_crawled INTEGER DEFAULT 1,
                    detail_crawl_time TEXT,
                    UNIQUE(cid)
                )
            ''')
            
            # 插入数据
            cursor.execute('''
                INSERT OR REPLACE INTO dmm_works
                (cid, title, number, studio, detail_url, crawl_time,
                 release_date, sale_date, duration, actress, director, series, maker, label,
                 genre, rating, detail_crawled, detail_crawl_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                detail_info.get('cid'),
                detail_info.get('title'),
                detail_info.get('number'),
                detail_info.get('studio'),
                detail_info.get('url'),
                detail_info.get('crawl_time'),
                detail_info.get('release_date'),
                detail_info.get('sale_date'),
                detail_info.get('duration'),
                detail_info.get('actress'),
                detail_info.get('director'),
                detail_info.get('series'),
                detail_info.get('maker'),
                detail_info.get('label'),
                detail_info.get('genre'),
                detail_info.get('rating'),
                1,
                detail_info.get('crawl_time')
            ))
            
            conn.commit()
            conn.close()
            print("✅ 数据已保存到数据库")
            
        except Exception as e:
            print(f"❌ 保存数据库失败: {e}")
    
    def _format_database_result(self, row, source: str) -> Dict:
        """格式化数据库结果"""
        return {
            "success": True,
            "source": source,
            "cid": row[1] if len(row) > 1 else "",
            "title": row[2] if len(row) > 2 else "",
            "number": row[3] if len(row) > 3 else "",
            "studio": row[4] if len(row) > 4 else "",
            "url": row[5] if len(row) > 5 else "",
            "actress": row[11] if len(row) > 11 else "",
            "duration": row[10] if len(row) > 10 else "",
            "maker": row[14] if len(row) > 14 else "",
            "rating": row[17] if len(row) > 17 else "",
            "release_date": row[8] if len(row) > 8 else "",
            "genre": row[16] if len(row) > 16 else "",
            "director": row[12] if len(row) > 12 else "",
            "series": row[13] if len(row) > 13 else "",
            "label": row[15] if len(row) > 15 else "",
            "message": f"✅ 从{source}获取完整信息"
        }

# 使用示例
if __name__ == "__main__":
    # 创建集成爬虫搜索服务
    search_service = CrawlerIntegratedSearch(enable_realtime_crawl=True)
    
    # 测试搜索
    test_queries = ["CAWD-797", "MILK-251", "cawd00797"]
    
    for query in test_queries:
        print(f"\n🧪 测试搜索: {query}")
        result = search_service.search_with_crawl(query)
        
        if result and result.get("success"):
            print(f"  ✅ 找到: {result.get('title', 'N/A')}")
            print(f"  📍 CID: {result.get('cid', 'N/A')}")
            print(f"  🎬 演员: {result.get('actress', 'N/A')}")
            print(f"  📊 来源: {result.get('source', 'N/A')}")
        else:
            print(f"  ❌ 未找到: {result.get('message', 'N/A')}")
    
    # 测试手动搜索
    print(f"\n🔍 测试手动搜索（强制爬取）:")
    manual_result = search_service.manual_search_and_crawl("CAWD-797")
    print(f"结果: {manual_result.get('message', 'N/A')}")
