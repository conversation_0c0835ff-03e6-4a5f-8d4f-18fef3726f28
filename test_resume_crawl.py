#!/usr/bin/env python3
"""
测试续爬功能
"""

from dmm_firefox_crawler import DMMFirefoxCrawler

def test_check_existing_pages():
    """测试检查已存在页面功能"""
    print("📊 测试检查已存在页面功能")
    print("=" * 50)
    
    crawler = DMMFirefoxCrawler()
    
    # 检查前20页中哪些已存在
    existing_pages = crawler.check_existing_pages(1, 20)
    
    print(f"📋 数据库中已存在的页面: {existing_pages}")
    print(f"📊 已存在页面数量: {len(existing_pages)}")
    
    if existing_pages:
        print(f"✅ 最大页面: {max(existing_pages)}")
        print(f"📈 建议从第 {max(existing_pages) + 1} 页开始续爬")
    else:
        print("⚠️ 数据库中没有找到任何页面数据")
    
    return existing_pages

def test_database_status():
    """测试数据库状态"""
    print("\n📊 测试数据库状态")
    print("=" * 50)
    
    import sqlite3
    import os
    
    db_file = "dmm_firefox_database.db"
    
    if not os.path.exists(db_file):
        print("❌ 数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 总作品数
        cursor.execute("SELECT COUNT(*) FROM dmm_works")
        total_works = cursor.fetchone()[0]
        
        # 按页面统计
        cursor.execute("""
            SELECT page_number, COUNT(*) as count 
            FROM dmm_works 
            GROUP BY page_number 
            ORDER BY page_number
        """)
        page_stats = cursor.fetchall()
        
        print(f"📈 总作品数: {total_works}")
        print(f"📄 页面统计:")
        
        for page_num, count in page_stats:
            print(f"   第{page_num}页: {count}个作品")
        
        # 最新爬取时间
        cursor.execute("SELECT MAX(crawl_time) FROM dmm_works")
        latest_crawl = cursor.fetchone()[0]
        
        if latest_crawl:
            print(f"🕐 最新爬取时间: {latest_crawl}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")

def suggest_resume_strategy():
    """建议续爬策略"""
    print("\n💡 续爬策略建议")
    print("=" * 50)
    
    crawler = DMMFirefoxCrawler()
    existing_pages = crawler.check_existing_pages(1, 20)
    
    if not existing_pages:
        print("🆕 数据库为空，建议从第1页开始爬取")
        print("   推荐命令: 选择选项2（爬取前5页）")
        return
    
    max_page = max(existing_pages)
    missing_pages = []
    
    # 检查是否有缺失的页面
    for i in range(1, max_page + 1):
        if i not in existing_pages:
            missing_pages.append(i)
    
    print(f"📊 当前状态:")
    print(f"   已爬取页面: {existing_pages}")
    print(f"   最大页面: {max_page}")
    
    if missing_pages:
        print(f"   缺失页面: {missing_pages}")
        print(f"\n🔧 建议操作:")
        print(f"   1. 补爬缺失页面: {missing_pages}")
        print(f"   2. 续爬新页面: 从第{max_page + 1}页开始")
    else:
        print(f"   ✅ 前{max_page}页完整")
        print(f"\n🚀 建议操作:")
        print(f"   续爬新页面: 从第{max_page + 1}页开始")
        print(f"   推荐范围: {max_page + 1}-{max_page + 5}")
    
    print(f"\n🎯 使用续爬模式:")
    print(f"   1. 运行: python3 dmm_firefox_crawler.py")
    print(f"   2. 选择: 选项5（续爬模式）")
    print(f"   3. 输入: {max_page + 1}-{max_page + 5}")

def main():
    """主测试函数"""
    print("🔄 DMM Firefox爬虫续爬功能测试")
    print("=" * 60)
    
    try:
        # 测试数据库状态
        test_database_status()
        
        # 测试检查已存在页面
        existing_pages = test_check_existing_pages()
        
        # 建议续爬策略
        suggest_resume_strategy()
        
        print("\n" + "=" * 60)
        print("✅ 续爬功能测试完成！")
        print("💡 现在你可以:")
        print("   - 🔍 查看数据库中已有的页面")
        print("   - ⏭️ 自动跳过已存在的页面")
        print("   - 🚀 从断点继续爬取新内容")
        print("   - 📊 获得准确的爬取建议")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
