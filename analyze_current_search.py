#!/usr/bin/env python3
"""
分析现有搜索系统结构
"""
import sqlite3
import os
import json
from pathlib import Path

def analyze_databases():
    """分析现有数据库"""
    print("📊 分析现有数据库结构...")
    
    databases = [
        "data/fast_dmm.db",
        "mmp/fast_dmm.db", 
        "dmm_firefox_database.db"
    ]
    
    for db_file in databases:
        if os.path.exists(db_file):
            print(f"\n🗄️ 数据库: {db_file}")
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # 获取所有表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                
                for table_name, in tables:
                    print(f"  📋 表: {table_name}")
                    
                    # 获取表结构
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = cursor.fetchall()
                    
                    for col in columns:
                        print(f"    - {col[1]} ({col[2]})")
                    
                    # 获取数据量
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    print(f"    📈 数据量: {count} 条")
                
                conn.close()
                
            except Exception as e:
                print(f"    ❌ 分析失败: {e}")
        else:
            print(f"\n❌ 数据库不存在: {db_file}")

def analyze_search_modules():
    """分析搜索模块"""
    print("\n🔍 分析搜索模块...")
    
    search_files = [
        "modules/fast_dmm_search.py",
        "modules/search_detail.py", 
        "mmp/fast_dmm_search.py",
        "dmm_tools.py"
    ]
    
    for file_path in search_files:
        if os.path.exists(file_path):
            print(f"\n📄 文件: {file_path}")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 查找关键函数
                if "def search" in content:
                    print("  ✅ 包含搜索函数")
                if "def smart_search" in content:
                    print("  🧠 包含智能搜索")
                if "def rename" in content:
                    print("  📝 包含重命名功能")
                if "sqlite3" in content:
                    print("  🗄️ 使用SQLite数据库")
                if "requests" in content:
                    print("  🌐 使用网络请求")
                    
            except Exception as e:
                print(f"    ❌ 分析失败: {e}")
        else:
            print(f"\n❌ 文件不存在: {file_path}")

def analyze_config_files():
    """分析配置文件"""
    print("\n⚙️ 分析配置文件...")
    
    config_files = [
        "studio_mappings_all.json",
        "mmp/dmm_studio_mappings.json",
        "number_prefixes.json",
        "h_prefix_numbers.json"
    ]
    
    for file_path in config_files:
        if os.path.exists(file_path):
            print(f"\n📋 配置文件: {file_path}")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                if isinstance(data, dict):
                    print(f"  📊 字典类型，包含 {len(data)} 个键")
                    # 显示前几个键
                    keys = list(data.keys())[:5]
                    print(f"  🔑 示例键: {keys}")
                elif isinstance(data, list):
                    print(f"  📊 列表类型，包含 {len(data)} 个元素")
                    if data:
                        print(f"  📝 示例元素: {data[:3]}")
                        
            except Exception as e:
                print(f"    ❌ 分析失败: {e}")
        else:
            print(f"\n❌ 配置文件不存在: {file_path}")

def analyze_rename_templates():
    """分析重命名模板"""
    print("\n📝 分析重命名模板...")
    
    # 从代码中提取重命名模板
    template_files = [
        "dmm_tools.py",
        "modules/search_detail.py",
        "gradio_app.py"
    ]
    
    for file_path in template_files:
        if os.path.exists(file_path):
            print(f"\n📄 检查文件: {file_path}")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 查找重命名模板
                import re
                patterns = re.findall(r'f"([^"]*\{[^}]*\}[^"]*)"', content)
                for pattern in patterns:
                    if any(keyword in pattern for keyword in ['base_code', 'cid', 'suffix', '.mp4']):
                        print(f"  🎯 发现模板: {pattern}")
                        
            except Exception as e:
                print(f"    ❌ 分析失败: {e}")

if __name__ == "__main__":
    print("🔍 MMA项目搜索系统分析报告")
    print("=" * 50)
    
    analyze_databases()
    analyze_search_modules() 
    analyze_config_files()
    analyze_rename_templates()
    
    print("\n✅ 分析完成！")
