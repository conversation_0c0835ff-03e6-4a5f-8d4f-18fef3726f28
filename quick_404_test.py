#!/usr/bin/env python3
"""
快速404检测测试
"""

from dmm_firefox_crawler import DMMFirefoxCrawler

def quick_test():
    """快速测试404检测"""
    print("⚡ 快速404检测测试")
    print("=" * 40)
    
    crawler = DMMFirefoxCrawler()
    
    # 初始化浏览器
    if not crawler.init_driver():
        print("❌ Firefox浏览器初始化失败")
        return
    
    try:
        # 只测试第15页（已知的404页面）
        print("🧪 测试第15页（应该是404）")
        success, works, failed = crawler.crawl_single_page(15)
        
        if success:
            print(f"✅ 第15页存在，找到 {len(works)} 个作品")
        else:
            print("❌ 第15页不存在（404错误）")
            print("💡 这是正确的行为，说明404检测正常工作")
        
    except Exception as e:
        print(f"❌ 测试出错: {e}")
    
    finally:
        crawler.close()
        print("✅ 测试完成")

if __name__ == "__main__":
    quick_test()
