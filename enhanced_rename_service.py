#!/usr/bin/env python3
"""
增强重命名服务 - 集成DMM Firefox数据库
支持现有模板格式，优先使用完整数据
"""

import os
import re
from typing import List, Dict, Optional, Tuple
from pathlib import Path
from enhanced_search_service import EnhancedSearchService

class EnhancedRenameService:
    """增强重命名服务"""
    
    def __init__(self):
        self.search_service = EnhancedSearchService()
        
        # 重命名模板（保持现有格式）
        self.template = "{base_code} ({cid}){suffix}.mp4"
        
        # 支持的后缀模式
        self.suffix_patterns = [
            r'(-4K)',
            r'(-C)', 
            r'(-4K-C)',
            r'(-cd\d+)',
            r'(-4K-cd\d+)',
            r'(-C-cd\d+)', 
            r'(-4K-C-cd\d+)'
        ]
    
    def extract_number_from_filename(self, filename: str) -> Optional[str]:
        """从文件名提取番号"""
        # 移除扩展名
        name_without_ext = os.path.splitext(filename)[0]
        
        # 多种番号提取模式
        patterns = [
            r'^([A-Z0-9]+-\d+)',  # 标准格式：CAWD-797
            r'([A-Z]+[-_]?\d+)',  # 变体格式：CAWD797, CAWD_797
            r'([A-Z]+\d+)',       # 紧凑格式：CAWD797
        ]
        
        for pattern in patterns:
            match = re.search(pattern, name_without_ext.upper())
            if match:
                number = match.group(1)
                # 标准化格式
                if '-' not in number and re.match(r'^[A-Z]+\d+$', number):
                    # 在字母和数字之间添加连字符
                    number = re.sub(r'([A-Z]+)(\d+)', r'\1-\2', number)
                return number
        
        return None
    
    def extract_suffix_from_filename(self, filename: str) -> str:
        """提取文件名中的后缀"""
        name_without_ext = os.path.splitext(filename)[0]
        
        # 移除基础番号部分
        number = self.extract_number_from_filename(filename)
        if not number:
            return ""
        
        # 查找后缀
        base_pattern = number.replace("-", "[-_]?")
        remaining = re.sub(f'^{base_pattern}', '', name_without_ext, flags=re.IGNORECASE)
        
        # 匹配已知后缀模式
        for pattern in self.suffix_patterns:
            if re.search(pattern, remaining, re.IGNORECASE):
                return re.search(pattern, remaining, re.IGNORECASE).group(1)
        
        # 返回剩余部分作为后缀
        return remaining if remaining else ""
    
    def generate_new_filename(self, old_filename: str, search_result: Dict) -> Optional[str]:
        """生成新文件名"""
        if not search_result or not search_result.get("success"):
            return None
        
        # 提取基础信息
        number = self.extract_number_from_filename(old_filename)
        suffix = self.extract_suffix_from_filename(old_filename)
        cid = search_result.get("cid", "")
        
        if not number or not cid:
            return None
        
        # 生成新文件名
        new_name = self.template.format(
            base_code=number,
            cid=cid,
            suffix=suffix
        )
        
        return new_name
    
    def preview_rename(self, file_paths: List[str]) -> List[Dict]:
        """预览重命名结果"""
        results = []
        
        for file_path in file_paths:
            filename = os.path.basename(file_path)
            
            # 只处理视频文件
            if not filename.lower().endswith('.mp4'):
                results.append({
                    "original": filename,
                    "new": filename,
                    "status": "skipped",
                    "reason": "非MP4文件"
                })
                continue
            
            # 提取番号
            number = self.extract_number_from_filename(filename)
            if not number:
                results.append({
                    "original": filename,
                    "new": filename,
                    "status": "skipped", 
                    "reason": "无法提取番号"
                })
                continue
            
            # 搜索信息
            search_result = self.search_service.search_by_number(number)
            
            if not search_result or not search_result.get("success"):
                results.append({
                    "original": filename,
                    "new": filename,
                    "status": "failed",
                    "reason": f"未找到番号 {number} 的信息",
                    "number": number
                })
                continue
            
            # 生成新文件名
            new_filename = self.generate_new_filename(filename, search_result)
            
            if not new_filename:
                results.append({
                    "original": filename,
                    "new": filename,
                    "status": "failed",
                    "reason": "无法生成新文件名",
                    "number": number
                })
                continue
            
            # 检查是否需要重命名
            if filename == new_filename:
                status = "unchanged"
                reason = "文件名已是正确格式"
            else:
                status = "ready"
                reason = "准备重命名"
            
            results.append({
                "original": filename,
                "new": new_filename,
                "status": status,
                "reason": reason,
                "number": number,
                "cid": search_result.get("cid", ""),
                "title": search_result.get("title", ""),
                "source": search_result.get("source", "")
            })
        
        return results
    
    def batch_rename(self, file_paths: List[str], dry_run: bool = True) -> Dict:
        """批量重命名文件"""
        preview_results = self.preview_rename(file_paths)
        
        if dry_run:
            return {
                "success": True,
                "dry_run": True,
                "results": preview_results,
                "summary": self._generate_summary(preview_results)
            }
        
        # 实际重命名
        rename_results = []
        success_count = 0
        error_count = 0
        
        for i, file_path in enumerate(file_paths):
            preview = preview_results[i]
            
            if preview["status"] != "ready":
                rename_results.append(preview)
                continue
            
            try:
                old_path = file_path
                new_path = os.path.join(
                    os.path.dirname(file_path),
                    preview["new"]
                )
                
                # 检查目标文件是否已存在
                if os.path.exists(new_path) and old_path != new_path:
                    preview["status"] = "failed"
                    preview["reason"] = "目标文件已存在"
                    error_count += 1
                else:
                    os.rename(old_path, new_path)
                    preview["status"] = "success"
                    preview["reason"] = "重命名成功"
                    success_count += 1
                
            except Exception as e:
                preview["status"] = "failed"
                preview["reason"] = f"重命名失败: {str(e)}"
                error_count += 1
            
            rename_results.append(preview)
        
        return {
            "success": True,
            "dry_run": False,
            "results": rename_results,
            "summary": {
                "total": len(file_paths),
                "success": success_count,
                "failed": error_count,
                "skipped": len([r for r in rename_results if r["status"] in ["skipped", "unchanged"]])
            }
        }
    
    def _generate_summary(self, results: List[Dict]) -> Dict:
        """生成统计摘要"""
        total = len(results)
        ready = len([r for r in results if r["status"] == "ready"])
        unchanged = len([r for r in results if r["status"] == "unchanged"])
        failed = len([r for r in results if r["status"] == "failed"])
        skipped = len([r for r in results if r["status"] == "skipped"])
        
        return {
            "total": total,
            "ready": ready,
            "unchanged": unchanged,
            "failed": failed,
            "skipped": skipped
        }

# 使用示例
if __name__ == "__main__":
    rename_service = EnhancedRenameService()
    
    # 测试文件列表
    test_files = [
        "CAWD-797.mp4",
        "CAWD-797-4K.mp4", 
        "MILK-251-C.mp4",
        "unknown-file.mp4"
    ]
    
    print("🧪 测试重命名预览:")
    results = rename_service.preview_rename(test_files)
    
    for result in results:
        print(f"📄 {result['original']}")
        print(f"  → {result['new']}")
        print(f"  状态: {result['status']}")
        print(f"  原因: {result['reason']}")
        if result.get('number'):
            print(f"  番号: {result['number']}")
        if result.get('cid'):
            print(f"  CID: {result['cid']}")
        print()
