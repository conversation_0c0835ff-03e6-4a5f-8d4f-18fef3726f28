#!/usr/bin/env python3
"""
FastDMM快速搜索引擎
整合高性能本地索引和智能搜索策略
"""

import time
import requests
from typing import List, Dict, Optional, Tuple
from .fast_dmm_database import FastDMMDatabase, SearchResult
from .mapping_manager import MappingManager


class FastDMMSearch:
    """快速DMM搜索引擎"""
    
    def __init__(self, db_file: str = "data/fast_dmm.db", config_file: str = "mmp/dmm_studio_mappings.json"):
        self.db = FastDMMDatabase(db_file, config_file)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
            'Cookie': 'age_check_done=1; cklg=ja'
        })
        
        # 性能统计
        self.stats = {
            "total_searches": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "average_response_time": 0.0
        }

        # 初始化映射管理器
        try:
            # 使用与数据库相同的路径结构
            db_path = db_file.replace("data/fast_dmm.db", "mmp/fast_dmm.db")
            self.mapping_manager = MappingManager(
                config_file="studio_mappings_all.json",
                db_file=db_path,
                enable_db_sync=True
            )
            print("✅ 映射管理器初始化成功")
        except Exception as e:
            print(f"⚠️ 映射管理器初始化失败: {e}")
            self.mapping_manager = None
    
    def smart_search(self, code: str) -> List[SearchResult]:
        """智能搜索：多策略组合"""
        start_time = time.time()
        self.stats["total_searches"] += 1
        
        try:
            # 1. 首先在本地索引中搜索
            local_results = self.db.search_in_index(code)
            if local_results:
                self.stats["cache_hits"] += 1
                self._update_response_time(start_time)
                print(f"🚀 快速搜索命中缓存: {code} -> {local_results[0].dmm_cid}")
                return local_results
            
            self.stats["cache_misses"] += 1
            
            # 2. 解析番号并生成可能的结果
            parsed = self.db.parse_code(code)
            if not parsed:
                self._update_response_time(start_time)
                return []
            
            studio, number = parsed
            
            # 3. 如果厂商在映射中，直接生成结果
            if studio in self.db.studio_mappings:
                dmm_cid = self.db.generate_dmm_cid(studio, number)
                if dmm_cid:
                    url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={dmm_cid}/"
                    
                    # 添加到索引
                    if self.db.add_to_index(studio, number, dmm_cid, url, confidence=0.8, source="fast_generated"):
                        # 再次搜索本地索引
                        local_results = self.db.search_in_index(code)
                        if local_results:
                            # 异步验证URL（可选）
                            result = local_results[0]
                            if self._should_verify_url(result):
                                if self.verify_dmm_url(result.url):
                                    result.confidence = 1.0
                                    result.source = "fast_generated+verified"
                                    self.db.mark_as_verified(result.dmm_cid)
                                    print(f"✅ 快速搜索+验证成功: {code} -> {result.dmm_cid}")
                                else:
                                    print(f"⚠️ 快速搜索生成但验证失败: {code} -> {result.dmm_cid}")
                            else:
                                print(f"🎯 快速搜索生成成功: {code} -> {result.dmm_cid}")
                            
                            self._update_response_time(start_time)
                            return local_results
            
            # 4. 如果快速搜索失败，返回空结果（让调用方使用备用搜索）
            self._update_response_time(start_time)
            return []
            
        except Exception as e:
            print(f"❌ 快速搜索异常: {e}")
            self._update_response_time(start_time)
            return []
    
    def _should_verify_url(self, result: SearchResult) -> bool:
        """判断是否需要验证URL"""
        # 只对新生成的、未验证的结果进行验证
        return not result.verified and result.source in ["fast_generated", "generated"]
    
    def verify_dmm_url(self, url: str) -> bool:
        """验证DMM URL是否有效"""
        try:
            response = self.session.head(url, timeout=5)
            return response.status_code == 200
        except Exception:
            return False
    
    def _update_response_time(self, start_time: float):
        """更新平均响应时间"""
        response_time = time.time() - start_time
        total_searches = self.stats["total_searches"]
        current_avg = self.stats["average_response_time"]
        
        # 计算新的平均值
        self.stats["average_response_time"] = (current_avg * (total_searches - 1) + response_time) / total_searches
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计信息"""
        cache_hit_rate = self.stats["cache_hits"] / self.stats["total_searches"] if self.stats["total_searches"] > 0 else 0
        
        return {
            "total_searches": self.stats["total_searches"],
            "cache_hits": self.stats["cache_hits"],
            "cache_misses": self.stats["cache_misses"],
            "cache_hit_rate": cache_hit_rate,
            "average_response_time_ms": self.stats["average_response_time"] * 1000,
            "database_stats": self.db.get_database_stats()
        }
    
    def is_studio_supported(self, studio: str) -> bool:
        """检查厂商是否支持快速搜索"""
        return studio.upper() in self.db.studio_mappings
    
    def get_supported_studios(self) -> List[str]:
        """获取支持的厂商列表"""
        return list(self.db.studio_mappings.keys())
    
    def build_index_for_studio(self, studio: str, number_range: Tuple[int, int] = (1, 500)) -> int:
        """为特定厂商构建索引"""
        if not self.is_studio_supported(studio):
            print(f"❌ 不支持的厂商: {studio}")
            return 0
        
        return self.db.build_popular_index([studio], number_range)
    
    def build_popular_index(self, top_studios: int = 10, number_range: Tuple[int, int] = (1, 300)) -> int:
        """构建热门厂商索引"""
        # 选择最热门的厂商
        popular_studios = [
            "MILK", "SSIS", "STARS", "PRED", "IPX", "MIDE", "MIAA", "MIDV", 
            "FSDSS", "MIMK", "PPPD", "JUFE", "EBOD", "MEYD", "JUL", "CAWD",
            "DASD", "DASS", "HUNTB", "ROYD", "SAME", "SDMM"
        ]
        
        # 只选择支持的厂商
        supported_studios = [s for s in popular_studios[:top_studios] if self.is_studio_supported(s)]
        
        return self.db.build_popular_index(supported_studios, number_range)
    
    def cleanup_database(self, days: int = 30) -> int:
        """清理数据库"""
        return self.db.cleanup_old_records(days)
    
    def verify_random_urls(self, count: int = 50) -> Dict:
        """随机验证一些URL"""
        return self.db.verify_index_urls(count)
    
    def search_with_fallback(self, code: str, fallback_search_func=None) -> Tuple[List[SearchResult], str]:
        """
        带备用搜索的智能搜索
        返回: (搜索结果, 搜索方式)
        """
        # 首先尝试快速搜索
        fast_results = self.smart_search(code)
        if fast_results:
            return fast_results, "fast_search"
        
        # 如果快速搜索失败且提供了备用搜索函数
        if fallback_search_func:
            try:
                fallback_result = fallback_search_func(code)
                if fallback_result and fallback_result.get("success"):
                    # 将备用搜索结果转换为标准格式
                    result = SearchResult(
                        code=fallback_result.get("code", code),
                        dmm_cid=fallback_result.get("cid", ""),
                        url=fallback_result.get("url", ""),
                        confidence=0.9,
                        source="fallback_search",
                        studio=fallback_result.get("label", ""),
                        verified=True
                    )
                    
                    # 尝试将结果添加到快速搜索索引中
                    parsed = self.db.parse_code(code)
                    if parsed:
                        studio, number = parsed
                        self.db.add_to_index(
                            studio, number, result.dmm_cid, result.url,
                            confidence=0.9, source="fallback_learned", verified=True
                        )
                        print(f"📚 从备用搜索学习: {code} -> {result.dmm_cid}")
                    
                    return [result], "fallback_search"
            except Exception as e:
                print(f"❌ 备用搜索失败: {e}")
        
        return [], "no_result"
    
    def get_poster_url_direct(self, studio: str, number: int) -> Optional[str]:
        """直接生成海报URL（如果厂商支持）"""
        if not self.is_studio_supported(studio):
            return None
        
        dmm_cid = self.db.generate_dmm_cid(studio, number)
        if dmm_cid:
            return f'https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{dmm_cid}/{dmm_cid}ps.jpg'
        
        return None
    
    def get_poster_urls_for_cid(self, dmm_cid: str) -> Dict[str, str]:
        """根据CID生成海报URL"""
        return {
            "poster_url": f'https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{dmm_cid}/{dmm_cid}ps.jpg',
            "thumb_url": f'https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{dmm_cid}/{dmm_cid}pl.jpg'
        }
    
    def reset_stats(self):
        """重置性能统计"""
        self.stats = {
            "total_searches": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "average_response_time": 0.0
        }

    def intelligent_search(self, code: str) -> Dict:
        """智能搜索：自动检测映射类型并返回相应结果"""
        start_time = time.time()
        self.stats["total_searches"] += 1

        # 解析番号
        parsed = self.db.parse_code(code)
        if not parsed:
            return self._create_search_response(False, "番号格式无法识别", [])

        studio, number = parsed

        # 检查映射管理器是否可用
        if not self.mapping_manager:
            print("⚠️ 映射管理器不可用")
            return self._create_search_response(False, "映射管理器不可用", [])

        # 检测映射类型
        mapping_info = self.mapping_manager.get_mapping_info(studio)

        if not mapping_info['has_mapping']:
            # 没有映射信息，返回失败
            print(f"🔍 厂商 {studio} 无映射信息")
            return self._create_search_response(False, f"厂商 {studio} 暂不支持", [])

        if mapping_info['type'] == 'single':
            # 单一映射，直接构建CID并搜索
            print(f"🎯 厂商 {studio} 单一映射: {mapping_info['primary']}")

            # 构建CID
            padded_number = str(number).zfill(5)
            dmm_cid = f"{mapping_info['primary']}{padded_number}"
            url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={dmm_cid}/"

            # 创建结果
            result = SearchResult(
                code=code,
                dmm_cid=dmm_cid,
                url=url,
                confidence=1.0,
                source="fast_generated+verified",
                studio=studio,
                verified=False
            )

            # 检查是否已存在，避免重复添加
            existing = self.db.search_by_cid(dmm_cid)
            if not existing:
                self.db.add_to_index(studio, number, dmm_cid, url, confidence=1.0, source="single_mapping", verified=False)
                print(f"✅ 已添加到索引: {code} -> {dmm_cid}")
            else:
                print(f"♻️ 索引已存在: {code} -> {dmm_cid}")

            return self._create_search_response(True, f"✅ 搜索成功！置信度: {result.confidence:.2f}", [result], is_multiple=False)

        elif mapping_info['type'] == 'multiple':
            # 多重映射，生成所有可能的结果
            print(f"🔀 厂商 {studio} 多重映射: {mapping_info['primary']} + {len(mapping_info['alternatives'])}个备选")
            all_results = self._generate_multiple_results(studio, number, mapping_info)
            if all_results:
                return self._create_search_response(True, "找到多个可能结果", all_results, is_multiple=True)
            else:
                return self._create_search_response(False, "未找到任何结果", [])

        self._update_response_time(start_time)
        return self._create_search_response(False, "未知错误", [])

    def _generate_multiple_results(self, studio: str, number: int, mapping_info: Dict) -> List[SearchResult]:
        """生成多重映射的所有结果（带验证和去重）"""
        results = []

        # 按优先级排序：primary first, then alternatives
        ordered_mappings = [mapping_info['primary']] + mapping_info['alternatives']

        # 🔍 验证和去重映射（可选，用于提高质量）
        if hasattr(self, 'enable_mapping_validation') and self.enable_mapping_validation:
            validated_mappings = self._validate_mappings_if_needed(studio, number, ordered_mappings)
            if validated_mappings:
                ordered_mappings = validated_mappings

        # 用于去重的CID集合
        seen_cids = set()

        for i, dmm_prefix in enumerate(ordered_mappings):
            padded_number = str(number).zfill(5)
            dmm_cid = f"{dmm_prefix}{padded_number}"

            # 🔄 去重检查
            if dmm_cid in seen_cids:
                print(f"   🔄 跳过重复CID: {dmm_cid}")
                continue

            seen_cids.add(dmm_cid)
            url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={dmm_cid}/"

            # 检查本地索引（通过具体的CID搜索）
            local_results = self.db.search_by_cid(dmm_cid)

            if local_results:
                # 使用现有的索引结果，但更新映射信息
                for result in local_results:
                    result.mapping_type = 'primary' if i == 0 else 'alternative'
                    result.mapping_priority = i
                    result.confidence = 0.95 if i == 0 else 0.8
                    result.source = f"multi_mapping_{'primary' if i == 0 else 'alternative'}"
                results.extend(local_results)
                print(f"   ✅ 使用现有索引: {dmm_cid}")
            else:
                # 创建新结果 - 每个映射前缀都生成不同的CID
                result = SearchResult(
                    code=f"{studio}-{number}",
                    dmm_cid=dmm_cid,  # 这里每次都是不同的CID
                    url=url,
                    confidence=0.95 if i == 0 else 0.8,
                    source=f"multi_mapping_{'primary' if i == 0 else 'alternative'}",
                    studio=studio
                )
                result.mapping_type = 'primary' if i == 0 else 'alternative'
                result.mapping_priority = i
                result.number = number
                results.append(result)

                # 检查是否已存在，避免重复添加
                existing = self.db.search_by_cid(dmm_cid)
                if not existing:
                    self.db.add_to_index(studio, number, dmm_cid, url, result.confidence, result.source)
                    print(f"   🆕 创建新结果: {dmm_cid}")
                else:
                    print(f"   ♻️ 结果已存在: {dmm_cid}")

        print(f"   📊 生成 {len(results)} 个去重后的多重映射结果")
        return results

    def _validate_mappings_if_needed(self, studio: str, number: int, mappings: List[str]) -> List[str]:
        """如果需要，验证映射（避免频繁验证）"""
        try:
            # 检查是否最近验证过
            cache_key = f"{studio}_{number}"

            if hasattr(self, '_validation_cache'):
                cached_result = self._validation_cache.get(cache_key)
                if cached_result:
                    print(f"   💾 使用缓存的验证结果: {cached_result}")
                    return cached_result
            else:
                self._validation_cache = {}

            # 只对重要的厂商进行在线验证
            important_studios = ['ID', 'SSIS', 'MILK', 'NEO', 'HODV']

            if studio in important_studios and len(mappings) > 3:
                print(f"   🔍 对重要厂商 {studio} 进行映射验证...")

                from modules.multi_mapping_validator import MultiMappingValidator
                validator = MultiMappingValidator()

                validation_result = validator.validate_and_deduplicate_mappings(studio, number, mappings)

                # 合并有效映射和发现的映射
                validated_mappings = validation_result['valid_mappings'] + validation_result['discovered_mappings']

                # 缓存结果（避免重复验证）
                self._validation_cache[cache_key] = validated_mappings

                print(f"   ✅ 验证完成，有效映射: {len(validated_mappings)}")
                return validated_mappings

            return mappings

        except Exception as e:
            print(f"   ⚠️ 映射验证失败，使用原始映射: {e}")
            return mappings

    def _create_search_response(self, success: bool, message: str, results: List, is_multiple: bool = False) -> Dict:
        """创建统一的搜索响应格式"""
        return {
            'success': success,
            'message': message,
            'is_multiple': is_multiple,
            'results': results,
            'count': len(results),
            'search_type': 'multiple' if is_multiple else 'single'
        }
