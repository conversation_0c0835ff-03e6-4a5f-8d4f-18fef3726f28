#!/usr/bin/env python3
"""
测试404页面检测功能
"""

from dmm_firefox_crawler import DMMFirefoxCrawler

def test_404_detection():
    """测试404页面检测"""
    print("🧪 测试404页面检测功能")
    print("=" * 50)

    crawler = DMMFirefoxCrawler()

    # 初始化浏览器
    if not crawler.init_driver():
        print("❌ Firefox浏览器初始化失败")
        return

    try:
        # 测试正常页面（第1页）
        print("\n1️⃣ 测试正常页面（第1页）")
        success, works, failed = crawler.crawl_single_page(1)
        print(f"   结果: 成功={success}, 作品数={len(works)}, 失败数={len(failed)}")

        # 测试可能存在的页面（第10页）
        print("\n2️⃣ 测试第10页")
        success, works, failed = crawler.crawl_single_page(10)
        print(f"   结果: 成功={success}, 作品数={len(works)}, 失败数={len(failed)}")

        # 测试404页面（第15页）
        print("\n3️⃣ 测试404页面（第15页）")
        success, works, failed = crawler.crawl_single_page(15)
        print(f"   结果: 成功={success}, 作品数={len(works)}, 失败数={len(failed)}")

        # 测试明显不存在的页面（第100页）
        print("\n4️⃣ 测试明显不存在的页面（第100页）")
        success, works, failed = crawler.crawl_single_page(100)
        print(f"   结果: 成功={success}, 作品数={len(works)}, 失败数={len(failed)}")

    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

    finally:
        crawler.close()
        print("\n✅ 测试完成，浏览器已关闭")

def test_auto_stop_crawling():
    """测试自动停止爬取功能"""
    print("\n\n🛑 测试自动停止爬取功能")
    print("=" * 50)

    crawler = DMMFirefoxCrawler()

    print("📋 测试说明:")
    print("   - 从第13页开始爬取到第20页")
    print("   - 当遇到404页面时应该自动停止")
    print("   - 验证智能停止机制")

    try:
        # 测试从第13页到第20页的爬取（应该在遇到404时自动停止）
        crawler.crawl_multiple_pages(13, 20)

    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

    print("\n✅ 自动停止测试完成")

if __name__ == "__main__":
    print("🔧 DMM Firefox爬虫404检测测试")
    print("=" * 60)

    try:
        # 测试404检测功能
        test_404_detection()

        # 测试自动停止功能
        test_auto_stop_crawling()

        print("\n" + "=" * 60)
        print("✅ 所有测试完成！")
        print("💡 现在爬虫可以:")
        print("   - ✅ 自动检测404页面")
        print("   - 🛑 遇到404时自动停止爬取")
        print("   - 📊 提供准确的页面数量信息")

    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
