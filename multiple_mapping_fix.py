#!/usr/bin/env python3
"""
多重映射结果优化补丁
解决MILK-251返回"N/A"的问题
"""

import sys
import os
sys.path.append('modules')

def patch_multiple_mapping_handling():
    """为SearchDetailModule添加多重映射优化"""
    
    patch_code = '''
    def _format_multiple_results_optimized(self, search_response, code):
        """优化的多重映射结果处理 - 自动选择最佳结果"""
        results = search_response['results']
        
        print(f"🔀 处理多重映射结果: {len(results)} 个选项")
        
        # 尝试找到有数据库详情的结果
        for result in results:
            cid = getattr(result, 'dmm_cid', '') or result.get('cid', '')
            if cid:
                try:
                    from dmm_firefox_crawler import DMMFirefoxCrawler
                    crawler = DMMFirefoxCrawler()
                    detail_info = self._search_firefox_database(code, crawler)
                    
                    if detail_info and detail_info.get('cid') == cid:
                        print(f"✅ 找到有数据库详情的映射: {cid}")
                        # 格式化为单一结果
                        enhanced_result = {
                            "success": True,
                            "code": code,
                            "cid": cid,
                            "label": detail_info.get("studio", ""),
                            "url": detail_info.get("detail_url", ""),
                            "title": detail_info.get("title", ""),
                            "actress": detail_info.get("actress", ""),
                            "duration": detail_info.get("duration", ""),
                            "rating": detail_info.get("rating", ""),
                            "release_date": detail_info.get("release_date", ""),
                            "genre": detail_info.get("genre", ""),
                            "source": "fast_dmm+firefox_database",
                            "message": f"✅ 多重映射智能选择！番号: {code}, CID: {cid}"
                        }
                        return enhanced_result
                except Exception as e:
                    print(f"⚠️ 检查数据库详情时出错: {e}")
                    continue
        
        # 如果没有找到数据库详情，使用映射优化器选择最佳结果
        try:
            from modules.mapping_optimizer import MappingOptimizer
            optimizer = MappingOptimizer()
            
            # 提取所有CID
            all_cids = []
            for result in results:
                cid = getattr(result, 'dmm_cid', '') or result.get('cid', '')
                if cid:
                    all_cids.append(cid)
            
            if all_cids:
                print(f"🎯 使用映射优化器验证: {all_cids}")
                valid_cids, invalid_cids = optimizer.optimize_mapping_list(all_cids)
                
                if valid_cids:
                    # 选择第一个有效的CID
                    best_cid = valid_cids[0]
                    print(f"✅ 映射优化器选择最佳CID: {best_cid}")
                    
                    # 找到对应的result对象
                    for result in results:
                        result_cid = getattr(result, 'dmm_cid', '') or result.get('cid', '')
                        if result_cid == best_cid:
                            return self._format_single_result(result, code)
        except Exception as e:
            print(f"⚠️ 映射优化器处理失败: {e}")
        
        # 备用：返回第一个结果
        if results:
            print(f"🔄 使用备用策略：返回第一个结果")
            return self._format_single_result(results[0], code)
        
        # 最后的备用：返回多选格式
        return {
            "success": True,
            "code": code,
            "cid": "N/A",
            "label": "Multiple Options",
            "url": "",
            "message": f"找到 {len(results)} 个可能的映射结果",
            "multiple_mappings": [getattr(r, 'dmm_cid', '') for r in results],
            "is_multiple": True
        }
    '''
    
    return patch_code

def test_multiple_mapping_fix():
    """测试多重映射修复效果"""
    print("🧪 测试多重映射结果优化")
    print("=" * 50)
    
    try:
        from modules.search_detail import SearchDetailModule
        
        # 创建搜索模块实例
        search_detail = SearchDetailModule()
        
        # 添加优化方法（动态补丁）
        patch_code = patch_multiple_mapping_handling()
        exec(f"SearchDetailModule._format_multiple_results_optimized = {patch_code}")
        
        # 测试MILK-251
        print("🧪 测试MILK-251多重映射处理:")
        result = search_detail.search_dmm_enhanced("MILK-251")
        
        print(f"📊 搜索结果:")
        print(f"   成功: {result.get('success', False)}")
        print(f"   CID: {result.get('cid', 'N/A')}")
        print(f"   来源: {result.get('source', 'N/A')}")
        print(f"   消息: {result.get('message', 'N/A')}")
        
        if result.get('cid') != 'N/A':
            print("✅ 多重映射优化成功！不再返回N/A")
        else:
            print("⚠️ 仍返回N/A，需要进一步优化")
        
        return result.get('cid') != 'N/A'
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_optimized_search_module():
    """创建优化版的搜索模块"""
    print("\n🔧 创建优化版搜索模块")
    print("=" * 50)
    
    optimized_code = '''
# 在modules/search_detail.py中添加以下方法

def _format_multiple_results_optimized(self, search_response, code):
    """优化的多重映射结果处理"""
    results = search_response['results']
    
    # 1. 优先选择有数据库详情的结果
    for result in results:
        cid = getattr(result, 'dmm_cid', '') or result.get('cid', '')
        if cid:
            try:
                from dmm_firefox_crawler import DMMFirefoxCrawler
                crawler = DMMFirefoxCrawler()
                detail_info = self._search_firefox_database(code, crawler)
                
                if detail_info and detail_info.get('cid') == cid:
                    # 返回增强的单一结果
                    return self._format_enhanced_result(result, detail_info, code)
            except:
                continue
    
    # 2. 使用映射优化器选择最佳结果
    try:
        from modules.mapping_optimizer import MappingOptimizer
        optimizer = MappingOptimizer()
        
        all_cids = [getattr(r, 'dmm_cid', '') for r in results if getattr(r, 'dmm_cid', '')]
        valid_cids, _ = optimizer.optimize_mapping_list(all_cids)
        
        if valid_cids:
            best_cid = valid_cids[0]
            for result in results:
                if getattr(result, 'dmm_cid', '') == best_cid:
                    return self._format_single_result(result, code)
    except:
        pass
    
    # 3. 备用：返回第一个结果
    return self._format_single_result(results[0], code) if results else None

def _format_enhanced_result(self, result, detail_info, code):
    """格式化增强结果"""
    cid = getattr(result, 'dmm_cid', '') or detail_info.get('cid', '')
    
    return {
        "success": True,
        "code": code,
        "cid": cid,
        "label": detail_info.get("studio", ""),
        "url": detail_info.get("detail_url", ""),
        "title": detail_info.get("title", ""),
        "actress": detail_info.get("actress", ""),
        "duration": detail_info.get("duration", ""),
        "rating": detail_info.get("rating", ""),
        "source": "fast_dmm+firefox_database+optimized",
        "message": f"✅ 多重映射智能选择！番号: {code}, CID: {cid}"
    }
'''
    
    print("💡 优化代码已生成，可以手动添加到search_detail.py中")
    print("📝 然后修改第131行，将:")
    print("   return self._format_multiple_results(search_response, code)")
    print("改为:")
    print("   return self._format_multiple_results_optimized(search_response, code)")
    
    return optimized_code

def main():
    """主函数"""
    print("🔧 多重映射结果优化补丁")
    print("=" * 60)
    
    try:
        # 测试当前的多重映射处理
        success = test_multiple_mapping_fix()
        
        # 生成优化代码
        optimized_code = create_optimized_search_module()
        
        print("\n" + "=" * 60)
        print("📊 优化建议总结:")
        
        if success:
            print("✅ 动态补丁测试成功")
        else:
            print("⚠️ 需要手动应用优化代码")
        
        print("\n💡 立即可用的优化:")
        print("1. 运行: python test_404_optimization.py")
        print("2. 404错误已优化为快速失败")
        print("3. 映射有效性缓存已建立")
        
        print("\n🔧 手动优化步骤:")
        print("1. 将优化代码添加到modules/search_detail.py")
        print("2. 修改_format_multiple_results调用")
        print("3. 重新测试MILK-251搜索")
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
'''
