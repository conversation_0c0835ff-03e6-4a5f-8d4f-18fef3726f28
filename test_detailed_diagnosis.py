#!/usr/bin/env python3
"""
详细诊断和修复测试
针对发现的问题进行深入测试和修复
"""

import sys
import time
sys.path.append('modules')

def test_force_crawl_details():
    """测试强制爬取详细信息"""
    print("🕷️ 测试强制爬取详细信息")
    print("=" * 50)
    
    try:
        from modules.search_detail import SearchDetailModule
        
        search_detail = SearchDetailModule()
        
        # 测试没有详细信息的番号，使用强制爬取
        test_cases = [
            "MILK-251",
            "PRED-123", 
            "IPX-123"
        ]
        
        for code in test_cases:
            print(f"\n🧪 强制爬取测试: {code}")
            
            # 1. 普通搜索
            print("   1️⃣ 普通搜索:")
            start_time = time.time()
            normal_result = search_detail.search_dmm_enhanced(code)
            normal_duration = time.time() - start_time
            
            print(f"      耗时: {normal_duration:.2f}秒")
            print(f"      标题: {normal_result.get('title', 'N/A')}")
            print(f"      演员: {normal_result.get('actress', 'N/A')}")
            print(f"      来源: {normal_result.get('source', 'N/A')}")
            
            # 2. 强制爬取
            print("   2️⃣ 强制爬取:")
            start_time = time.time()
            force_result = search_detail.search_dmm_enhanced(code, force_crawl=True)
            force_duration = time.time() - start_time
            
            print(f"      耗时: {force_duration:.2f}秒")
            print(f"      标题: {force_result.get('title', 'N/A')}")
            print(f"      演员: {force_result.get('actress', 'N/A')}")
            print(f"      来源: {force_result.get('source', 'N/A')}")
            
            # 对比结果
            if force_result.get('title') and force_result.get('title') != 'N/A':
                print(f"   ✅ 强制爬取成功获取详细信息")
            else:
                print(f"   ⚠️ 强制爬取仍未获取到详细信息")
        
        return True
        
    except Exception as e:
        print(f"❌ 强制爬取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_poster_download_fix():
    """测试海报下载修复"""
    print("\n\n🖼️ 测试海报下载修复")
    print("=" * 50)
    
    try:
        from modules.search_detail import SearchDetailModule
        
        search_detail = SearchDetailModule()
        
        # 测试有问题的CID
        test_cids = [
            ("PRED-123", "pred00123"),
            ("CAWD-797", "cawd00797"),  # 作为对照组
        ]
        
        for code, cid in test_cids:
            print(f"\n🧪 海报下载测试: {code} (CID: {cid})")
            
            try:
                start_time = time.time()
                poster_result = search_detail.get_poster_with_cid(cid)
                duration = time.time() - start_time
                
                print(f"   耗时: {duration:.2f}秒")
                print(f"   成功: {poster_result.get('success', False)}")
                
                if poster_result.get('success'):
                    # 安全检查各个字段
                    poster_bytes = poster_result.get('poster_bytes')
                    thumb_bytes = poster_result.get('thumb_bytes')
                    
                    poster_size = len(poster_bytes) if poster_bytes else 0
                    thumb_size = len(thumb_bytes) if thumb_bytes else 0
                    
                    print(f"   高清海报: {poster_size} bytes")
                    print(f"   缩略图: {thumb_size} bytes")
                    print(f"   海报URL: {poster_result.get('poster_url', 'N/A')}")
                    
                    if poster_size > 0:
                        print(f"   ✅ 高清海报下载成功")
                    else:
                        print(f"   ⚠️ 高清海报下载失败")
                    
                    if thumb_size > 0:
                        print(f"   ✅ 缩略图下载成功")
                    else:
                        print(f"   ⚠️ 缩略图下载失败或不存在")
                        
                else:
                    print(f"   ❌ 海报下载失败")
                    print(f"   原因: {poster_result.get('message', 'N/A')}")
                    
            except Exception as e:
                print(f"   ❌ 海报下载异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 海报下载修复测试失败: {e}")
        return False

def test_database_content():
    """测试数据库内容"""
    print("\n\n📊 测试数据库内容")
    print("=" * 50)
    
    try:
        import sqlite3
        import os
        
        db_file = "dmm_firefox_database.db"
        
        if not os.path.exists(db_file):
            print("❌ 数据库文件不存在")
            return False
        
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 检查数据库统计
        cursor.execute("SELECT COUNT(*) FROM dmm_works")
        total_count = cursor.fetchone()[0]
        print(f"📈 数据库总记录数: {total_count}")
        
        # 检查有详细信息的记录
        cursor.execute("SELECT COUNT(*) FROM dmm_works WHERE title IS NOT NULL AND title != ''")
        title_count = cursor.fetchone()[0]
        print(f"📝 有标题的记录: {title_count}")
        
        cursor.execute("SELECT COUNT(*) FROM dmm_works WHERE actress IS NOT NULL AND actress != ''")
        actress_count = cursor.fetchone()[0]
        print(f"🎬 有演员信息的记录: {actress_count}")
        
        # 检查测试CID是否在数据库中
        test_cids = ["cawd00797", "h_1240milk00251", "pred00123", "ipx00123"]
        
        print(f"\n🔍 检查测试CID在数据库中的情况:")
        for cid in test_cids:
            cursor.execute("SELECT cid, title, actress FROM dmm_works WHERE cid = ?", (cid,))
            result = cursor.fetchone()
            
            if result:
                cid_db, title, actress = result
                print(f"   ✅ {cid}: 标题={'有' if title else '无'}, 演员={'有' if actress else '无'}")
            else:
                print(f"   ❌ {cid}: 不在数据库中")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库内容检查失败: {e}")
        return False

def test_manual_crawl():
    """测试手动爬取特定CID"""
    print("\n\n🕷️ 测试手动爬取特定CID")
    print("=" * 50)
    
    try:
        from dmm_firefox_crawler import DMMFirefoxCrawler
        
        crawler = DMMFirefoxCrawler()
        
        # 测试手动爬取
        test_cids = ["pred00123", "ipx00123"]
        
        for cid in test_cids:
            print(f"\n🧪 手动爬取: {cid}")
            
            url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={cid}/"
            print(f"   URL: {url}")
            
            start_time = time.time()
            result = crawler.extract_detail_info(url, cid)
            duration = time.time() - start_time
            
            print(f"   耗时: {duration:.2f}秒")
            
            if result:
                print(f"   ✅ 爬取成功")
                print(f"   标题: {result.get('title', 'N/A')[:50]}...")
                print(f"   演员: {result.get('actress', 'N/A')}")
                print(f"   时长: {result.get('duration', 'N/A')}")
                print(f"   评分: {result.get('rating', 'N/A')}")
            else:
                print(f"   ❌ 爬取失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 手动爬取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_with_details():
    """测试搜索并获取详细信息的完整流程"""
    print("\n\n🔄 测试完整搜索流程")
    print("=" * 50)
    
    try:
        from modules.search_detail import SearchDetailModule
        
        search_detail = SearchDetailModule()
        
        # 测试一个新的番号，确保获取完整信息
        test_code = "PRED-123"
        
        print(f"🧪 完整流程测试: {test_code}")
        
        # 步骤1: 搜索获取CID
        print("   1️⃣ 搜索获取CID...")
        search_result = search_detail.search_dmm_enhanced(test_code)
        
        if not search_result.get('success'):
            print("   ❌ 搜索失败")
            return False
        
        cid = search_result.get('cid')
        print(f"   ✅ 获取CID: {cid}")
        
        # 步骤2: 强制爬取详细信息
        print("   2️⃣ 强制爬取详细信息...")
        detail_result = search_detail.search_dmm_enhanced(test_code, force_crawl=True)
        
        print(f"   标题: {detail_result.get('title', 'N/A')}")
        print(f"   演员: {detail_result.get('actress', 'N/A')}")
        print(f"   来源: {detail_result.get('source', 'N/A')}")
        
        # 步骤3: 下载海报
        print("   3️⃣ 下载海报...")
        poster_result = search_detail.get_poster_with_cid(cid)
        
        if poster_result.get('success'):
            poster_size = len(poster_result.get('poster_bytes', b''))
            print(f"   ✅ 海报下载成功: {poster_size} bytes")
        else:
            print(f"   ❌ 海报下载失败")
        
        # 步骤4: 保存数据
        print("   4️⃣ 保存数据...")
        save_result = search_detail.save_dmm_data_to_json(detail_result)
        
        if save_result.get('success'):
            print(f"   ✅ 数据保存成功")
        else:
            print(f"   ❌ 数据保存失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主诊断函数"""
    print("🔧 详细诊断和修复测试")
    print("=" * 60)
    
    print("📋 诊断项目:")
    print("   1. 强制爬取详细信息测试")
    print("   2. 海报下载修复测试")
    print("   3. 数据库内容检查")
    print("   4. 手动爬取测试")
    print("   5. 完整搜索流程测试")
    
    print("\n" + "=" * 60)
    
    test_results = {}
    
    try:
        # 1. 强制爬取测试
        force_crawl_success = test_force_crawl_details()
        test_results['强制爬取'] = force_crawl_success
        
        # 2. 海报下载修复
        poster_fix_success = test_poster_download_fix()
        test_results['海报修复'] = poster_fix_success
        
        # 3. 数据库内容检查
        db_check_success = test_database_content()
        test_results['数据库检查'] = db_check_success
        
        # 4. 手动爬取测试
        manual_crawl_success = test_manual_crawl()
        test_results['手动爬取'] = manual_crawl_success
        
        # 5. 完整流程测试
        full_process_success = test_search_with_details()
        test_results['完整流程'] = full_process_success
        
        # 诊断总结
        print("\n" + "=" * 60)
        print("📊 诊断结果总结:")
        
        for test_name, success in test_results.items():
            status = "✅ 正常" if success else "❌ 异常"
            print(f"   {test_name}: {status}")
        
        success_count = sum(test_results.values())
        total_tests = len(test_results)
        
        print(f"\n🎯 诊断成功率: {success_count}/{total_tests} ({success_count/total_tests:.1%})")
        
        # 问题分析和建议
        print(f"\n💡 问题分析和建议:")
        
        if not test_results.get('强制爬取', False):
            print("   ⚠️ 强制爬取功能异常，可能影响详细信息获取")
        
        if not test_results.get('海报修复', False):
            print("   ⚠️ 海报下载存在bug，需要修复缩略图处理")
        
        if not test_results.get('数据库检查', False):
            print("   ⚠️ 数据库内容可能不完整")
        
        if not test_results.get('手动爬取', False):
            print("   ⚠️ 手动爬取功能异常，可能是网络或解析问题")
        
        if success_count >= 4:
            print("   ✅ 大部分功能正常，只需要小幅修复")
        elif success_count >= 2:
            print("   ⚠️ 部分功能异常，需要针对性修复")
        else:
            print("   ❌ 多个功能异常，需要全面检查")
            
    except KeyboardInterrupt:
        print("\n⚠️ 诊断被用户中断")
    except Exception as e:
        print(f"\n❌ 诊断过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
