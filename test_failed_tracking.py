#!/usr/bin/env python3
"""
测试失败CID跟踪功能
"""

from dmm_firefox_crawler import DMMFirefoxCrawler

def test_failed_tracking():
    """测试失败跟踪功能"""
    print("🧪 测试失败CID跟踪功能")
    print("=" * 50)
    
    # 创建爬虫实例
    crawler = DMMFirefoxCrawler()
    
    # 测试单页爬取（会返回失败的CID）
    print("📋 测试说明:")
    print("   - 测试爬虫的失败CID跟踪功能")
    print("   - 会在爬取结束时显示失败的CID列表")
    print("   - 失败的CID会保存到文件中")
    
    # 运行测试
    crawler.crawl_first_page()

if __name__ == "__main__":
    test_failed_tracking()
