#!/usr/bin/env python3
"""
测试增强的搜索功能
"""

import sys
import os

# 添加模块路径
sys.path.append('modules')

from modules.search_detail import SearchDetailModule

def test_normal_search():
    """测试普通搜索"""
    print("🔍 测试普通搜索功能")
    print("=" * 50)

    search_detail = SearchDetailModule()

    test_queries = [
        "CAWD-797",
        "MILK-251",
        "PRED-123"
    ]

    for query in test_queries:
        print(f"\n🧪 普通搜索: {query}")
        print("-" * 30)

        result = search_detail.search_dmm_enhanced(query)

        if result and result.get("success"):
            print(f"✅ 搜索成功")
            print(f"📍 CID: {result.get('cid', 'N/A')}")
            print(f"📝 标题: {result.get('title', 'N/A')}")
            print(f"🎬 演员: {result.get('actress', 'N/A')}")
            print(f"⏱️ 时长: {result.get('duration', 'N/A')}")
            print(f"📊 来源: {result.get('source', 'N/A')}")
            print(f"💬 消息: {result.get('message', 'N/A')}")

            # 检查是否有多重映射结果
            if 'multiple_mappings' in result:
                print(f"🔀 多重映射结果数量: {len(result['multiple_mappings'])}")
                for i, mapping in enumerate(result['multiple_mappings'], 1):
                    print(f"   {i}️⃣ {mapping}")
        else:
            print(f"❌ 搜索失败")
            print(f"💬 消息: {result.get('message', 'N/A') if result else '无结果'}")

def test_multiple_mapping_search():
    """测试多重映射搜索功能 - 搜索所有可能的映射"""
    print("\n\n🔀 测试多重映射搜索功能")
    print("=" * 50)

    search_detail = SearchDetailModule()

    # 专门测试有多重映射的番号
    test_queries = [
        "MILK-251",  # 已知有多重映射: h_1240milk00251, milk00251
        "CAWD-797",  # 测试单一映射
        "PRED-123",  # 测试单一映射
        "IPX-123"    # 新增：测试IPX厂商
    ]

    for query in test_queries:
        print(f"\n🧪 多重映射搜索: {query}")
        print("-" * 30)

        # 生成所有可能的CID
        possible_cids = search_detail._generate_possible_cids(query)
        print(f"🎯 生成的CID数量: {len(possible_cids)}")
        print(f"🎯 CID列表: {possible_cids}")

        # 对每个CID都尝试搜索
        successful_results = []

        for i, cid in enumerate(possible_cids, 1):
            print(f"\n   🔍 测试CID {i}/{len(possible_cids)}: {cid}")

            # 直接测试这个CID是否能找到结果
            try:
                # 检查Firefox数据库
                from dmm_firefox_crawler import DMMFirefoxCrawler
                crawler = DMMFirefoxCrawler()

                # 使用CID直接查询数据库
                import sqlite3
                db_file = "dmm_firefox_database.db"
                db_result = None

                if os.path.exists(db_file):
                    try:
                        conn = sqlite3.connect(db_file)
                        cursor = conn.cursor()
                        cursor.execute("SELECT * FROM dmm_works WHERE cid = ? LIMIT 1", (cid,))
                        row = cursor.fetchone()

                        if row:
                            # 获取列名
                            columns = [description[0] for description in cursor.description]
                            db_result = dict(zip(columns, row))
                            print(f"   ✅ 数据库找到: {cid}")
                            print(f"      📝 标题: {db_result.get('title', 'N/A')}")
                            print(f"      🎬 演员: {db_result.get('actress', 'N/A')}")
                            print(f"      📊 来源: Firefox数据库")

                            successful_results.append({
                                'input_cid': cid,
                                'result_cid': cid,
                                'title': db_result.get('title'),
                                'source': 'firefox_database'
                            })
                        else:
                            print(f"   ⚠️ 数据库未找到: {cid}")

                        conn.close()
                    except Exception as e:
                        print(f"   ❌ 数据库查询错误: {e}")
                else:
                    print(f"   ⚠️ 数据库文件不存在")

                # 如果数据库没找到，尝试实时爬取
                if not db_result:
                    print(f"   🕷️ 尝试实时爬取: {cid}")
                    crawl_result = search_detail._realtime_crawl_details(cid, crawler)
                    if crawl_result:
                        print(f"   ✅ 实时爬取成功: {cid}")
                        print(f"      📝 标题: {crawl_result.get('title', 'N/A')}")
                        print(f"      🎬 演员: {crawl_result.get('actress', 'N/A')}")
                        print(f"      📊 来源: 实时爬取")

                        successful_results.append({
                            'input_cid': cid,
                            'result_cid': cid,
                            'title': crawl_result.get('title'),
                            'source': 'realtime_crawl'
                        })
                    else:
                        print(f"   ❌ 实时爬取失败: {cid}")

            except Exception as e:
                print(f"   ❌ 测试CID时出错: {e}")

        # 汇总结果
        print(f"\n📊 {query} 多重映射搜索汇总:")
        print(f"   🎯 测试的映射数量: {len(possible_cids)}")
        print(f"   ✅ 成功的映射数量: {len(successful_results)}")

        if successful_results:
            print(f"   📋 成功的映射详情:")
            for i, item in enumerate(successful_results, 1):
                print(f"      {i}️⃣ 输入CID: {item['input_cid']}")
                print(f"         返回CID: {item['result_cid']}")
                print(f"         标题: {item['title'] or 'N/A'}")
                print(f"         来源: {item['source']}")
        else:
            print(f"   ❌ 所有映射都未找到有效结果")

        print(f"   💡 建议: 优先使用成功率最高的映射格式")

def test_manual_search():
    """测试手动搜索（强制爬取）"""
    print("\n\n🕷️ 测试手动搜索（强制爬取）")
    print("=" * 50)

    search_detail = SearchDetailModule()
    
    # 选择一个测试番号
    test_query = "CAWD-797"
    
    print(f"🔍 手动搜索（强制爬取）: {test_query}")
    print("-" * 30)
    
    result = search_detail.manual_search_with_crawl(test_query)
    
    if result and result.get("success"):
        print(f"✅ 手动搜索成功")
        print(f"📍 CID: {result.get('cid', 'N/A')}")
        print(f"📝 标题: {result.get('title', 'N/A')}")
        print(f"🎬 演员: {result.get('actress', 'N/A')}")
        print(f"🏭 厂商: {result.get('label', 'N/A')}")
        print(f"📅 发售日: {result.get('release_date', 'N/A')}")
        print(f"🎭 类型: {result.get('genre', 'N/A')}")
        print(f"⭐ 评分: {result.get('rating', 'N/A')}")
        print(f"📊 来源: {result.get('source', 'N/A')}")
        print(f"💬 消息: {result.get('message', 'N/A')}")
    else:
        print(f"❌ 手动搜索失败")
        print(f"💬 消息: {result.get('message', 'N/A') if result else '无结果'}")

def test_force_crawl_flag():
    """测试强制爬取标志"""
    print("\n\n🚀 测试强制爬取标志")
    print("=" * 50)

    search_detail = SearchDetailModule()
    
    test_query = "MILK-251"
    
    print(f"🔍 测试番号: {test_query}")
    print("-" * 30)
    
    # 普通搜索
    print("1️⃣ 普通搜索:")
    result1 = search_detail.search_dmm_enhanced(test_query, force_crawl=False)
    if result1 and result1.get("success"):
        print(f"   ✅ 成功 - 来源: {result1.get('source', 'N/A')}")
    else:
        print(f"   ❌ 失败")
    
    # 强制爬取
    print("\n2️⃣ 强制爬取:")
    result2 = search_detail.search_dmm_enhanced(test_query, force_crawl=True)
    if result2 and result2.get("success"):
        print(f"   ✅ 成功 - 来源: {result2.get('source', 'N/A')}")
        print(f"   📝 标题: {result2.get('title', 'N/A')}")
    else:
        print(f"   ❌ 失败")

def test_database_check():
    """测试数据库状态"""
    print("\n\n📊 测试数据库状态")
    print("=" * 50)
    
    # 检查DMM Firefox数据库
    db_file = "dmm_firefox_database.db"
    if os.path.exists(db_file):
        print(f"✅ DMM Firefox数据库存在: {db_file}")
        
        try:
            import sqlite3
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM dmm_works")
            total_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM dmm_works WHERE title IS NOT NULL AND title != ''")
            title_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM dmm_works WHERE detail_crawled = 1")
            detailed_count = cursor.fetchone()[0]
            
            print(f"📈 总作品数: {total_count}")
            print(f"📝 有标题: {title_count} ({title_count/total_count*100:.1f}%)" if total_count > 0 else "📝 有标题: 0")
            print(f"🔍 详情完整: {detailed_count}")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 数据库查询失败: {e}")
    else:
        print(f"⚠️ DMM Firefox数据库不存在: {db_file}")
        print("将在首次爬取时自动创建")

def test_cid_generation():
    """测试CID生成（优化版：仅5位数字）"""
    print("\n\n🔧 测试CID生成（优化版：仅5位数字）")
    print("=" * 50)

    search_detail = SearchDetailModule()

    test_codes = [
        "CAWD-797",
        "MILK-251",
        "IPX-123",
        "cawd797",
        "PRED-123"
    ]

    for code in test_codes:
        print(f"\n🧪 测试番号: {code}")
        cids = search_detail._generate_possible_cids(code)
        print(f"   生成的CID: {cids} (仅5位数字格式，提升效率)")

def main():
    """主测试函数"""
    print("🧪 增强搜索功能测试")
    print("=" * 60)
    
    # 检查依赖
    try:
        import requests
        import bs4
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请安装: pip install requests beautifulsoup4")
        return
    
    print("\n" + "=" * 60)
    
    try:
        # 运行测试
        test_database_check()
        test_cid_generation()
        test_normal_search()
        test_multiple_mapping_search()  # 新增：多重映射测试
        test_manual_search()
        test_force_crawl_flag()

        print("\n" + "=" * 60)
        print("✅ 所有测试完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
