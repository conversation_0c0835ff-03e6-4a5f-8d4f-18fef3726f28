#!/usr/bin/env python3
"""
测试404错误优化效果
"""

import sys
import time
sys.path.append('modules')

from dmm_firefox_crawler import DMMFirefoxCrawler

def test_404_handling():
    """测试404错误处理优化"""
    print("🧪 测试404错误处理优化")
    print("=" * 50)
    
    crawler = DMMFirefoxCrawler()
    
    # 测试一个肯定不存在的CID
    test_cid = "nonexist99999"
    test_url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={test_cid}/"
    
    print(f"🔍 测试CID: {test_cid}")
    print(f"🌐 测试URL: {test_url}")
    print("-" * 30)
    
    start_time = time.time()
    
    # 测试优化后的详情提取
    result = crawler.extract_detail_info(test_url, test_cid)
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n📊 测试结果:")
    print(f"   耗时: {duration:.2f}秒")
    print(f"   结果: {'成功' if result else '失败（预期）'}")
    
    if duration < 5:
        print(f"   ✅ 优化成功！404错误快速失败，避免了长时间重试")
    else:
        print(f"   ⚠️ 仍需优化，耗时过长")
    
    return duration

def test_valid_vs_invalid():
    """对比有效和无效CID的处理时间"""
    print("\n\n⚡ 有效vs无效CID处理时间对比")
    print("=" * 50)
    
    crawler = DMMFirefoxCrawler()
    
    # 测试有效CID（从数据库中已知存在的）
    valid_cid = "cawd00797"
    valid_url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={valid_cid}/"
    
    print(f"1️⃣ 测试有效CID: {valid_cid}")
    start_time = time.time()
    valid_result = crawler.extract_detail_info(valid_url, valid_cid)
    valid_duration = time.time() - start_time
    print(f"   耗时: {valid_duration:.2f}秒")
    print(f"   结果: {'成功' if valid_result else '失败'}")
    
    # 测试无效CID
    invalid_cid = "invalid99999"
    invalid_url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={invalid_cid}/"
    
    print(f"\n2️⃣ 测试无效CID: {invalid_cid}")
    start_time = time.time()
    invalid_result = crawler.extract_detail_info(invalid_url, invalid_cid)
    invalid_duration = time.time() - start_time
    print(f"   耗时: {invalid_duration:.2f}秒")
    print(f"   结果: {'成功' if invalid_result else '失败（预期）'}")
    
    # 对比分析
    print(f"\n📊 性能对比:")
    if invalid_duration < 5:
        print(f"   ✅ 无效CID快速失败: {invalid_duration:.2f}秒")
    else:
        print(f"   ⚠️ 无效CID处理过慢: {invalid_duration:.2f}秒")
    
    if valid_result and invalid_duration < valid_duration:
        print(f"   ✅ 优化效果良好：无效CID比有效CID处理更快")
    
    return valid_duration, invalid_duration

def test_mapping_optimizer_integration():
    """测试映射优化器集成效果"""
    print("\n\n🎯 测试映射优化器集成")
    print("=" * 50)
    
    try:
        from modules.mapping_optimizer import MappingOptimizer
        
        optimizer = MappingOptimizer()
        
        # 测试已知的映射组合
        test_mappings = ["h_1240milk00251", "milk00251", "cawd00797", "invalid99999"]
        
        print(f"🔍 检查 {len(test_mappings)} 个映射...")
        
        start_time = time.time()
        valid_cids, invalid_cids = optimizer.optimize_mapping_list(test_mappings)
        duration = time.time() - start_time
        
        print(f"\n📊 优化结果:")
        print(f"   总耗时: {duration:.2f}秒")
        print(f"   有效映射: {valid_cids}")
        print(f"   无效映射: {invalid_cids}")
        
        # 显示缓存统计
        stats = optimizer.get_cache_stats()
        print(f"\n📈 缓存统计:")
        print(f"   总条目: {stats['total_entries']}")
        print(f"   有效率: {stats['validity_rate']:.2%}")
        print(f"   来源分布: {stats['sources']}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 映射优化器导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 404错误处理优化测试")
    print("=" * 60)
    
    try:
        # 测试404快速失败
        duration_404 = test_404_handling()
        
        # 测试有效vs无效对比
        valid_time, invalid_time = test_valid_vs_invalid()
        
        # 测试映射优化器
        optimizer_available = test_mapping_optimizer_integration()
        
        # 总结
        print("\n" + "=" * 60)
        print("📊 优化效果总结:")
        
        if duration_404 < 5:
            print("✅ 404错误快速失败优化成功")
        else:
            print("⚠️ 404错误处理仍需优化")
        
        if invalid_time < valid_time:
            print("✅ 无效CID处理速度优于有效CID")
        else:
            print("⚠️ 无效CID处理速度仍需优化")
        
        if optimizer_available:
            print("✅ 映射优化器集成成功")
        else:
            print("⚠️ 映射优化器需要修复")
        
        print("\n💡 下一步建议:")
        print("1. 继续优化多重映射结果处理")
        print("2. 集成映射优化器到主搜索流程")
        print("3. 实现智能映射排序")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
