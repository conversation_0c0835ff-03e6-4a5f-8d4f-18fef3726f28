#!/usr/bin/env python3
"""
测试映射优化器修复效果
"""

import sys
import os
sys.path.append('modules')

def test_mapping_optimizer_fix():
    """测试映射优化器修复"""
    print("🧪 测试映射优化器修复")
    print("=" * 50)
    
    try:
        from modules.mapping_optimizer import MappingOptimizer
        
        # 清除旧缓存，重新测试
        cache_file = "mapping_validity_cache.json"
        if os.path.exists(cache_file):
            os.remove(cache_file)
            print("🧹 清除旧缓存文件")
        
        optimizer = MappingOptimizer()
        
        # 测试已知的映射
        test_mappings = {
            "h_1240milk00251": "应该有效（数据库中存在）",
            "milk00251": "应该无效（302重定向）", 
            "cawd00797": "应该有效（数据库中存在）",
            "invalid99999": "应该无效（不存在的CID）",
            "nonexist99999": "应该无效（不存在的CID）"
        }
        
        print("🔍 重新检查映射有效性...")
        
        for cid, description in test_mappings.items():
            print(f"\n🧪 测试: {cid}")
            print(f"   预期: {description}")
            
            validity = optimizer.check_cid_validity(cid)
            
            status = "✅ 有效" if validity["valid"] else "❌ 无效"
            print(f"   结果: {status} - {validity['source']}")
            
            # 验证预期结果
            if cid in ["h_1240milk00251", "cawd00797"]:
                expected = True
            else:
                expected = False
            
            if validity["valid"] == expected:
                print(f"   🎯 判断正确")
            else:
                print(f"   ⚠️ 判断错误，预期: {'有效' if expected else '无效'}")
        
        # 显示缓存统计
        stats = optimizer.get_cache_stats()
        print(f"\n📊 新缓存统计:")
        print(f"   总条目: {stats['total_entries']}")
        print(f"   有效条目: {stats['valid_entries']}")
        print(f"   无效条目: {stats['invalid_entries']}")
        print(f"   有效率: {stats['validity_rate']:.2%}")
        print(f"   来源分布: {stats['sources']}")
        
        return stats['validity_rate'] < 1.0  # 应该有无效映射
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_mapping_with_optimizer():
    """测试多重映射结合优化器"""
    print("\n\n🔀 测试多重映射结合优化器")
    print("=" * 50)
    
    try:
        from modules.mapping_optimizer import MappingOptimizer
        from modules.search_detail import SearchDetailModule
        
        optimizer = MappingOptimizer()
        search_detail = SearchDetailModule()
        
        # 测试MILK-251的映射
        print("🧪 测试MILK-251映射优化:")
        
        # 生成映射
        possible_cids = search_detail._generate_possible_cids("MILK-251")
        print(f"🎯 生成的映射: {possible_cids}")
        
        # 使用优化器验证
        valid_cids, invalid_cids = optimizer.optimize_mapping_list(possible_cids)
        
        print(f"📊 优化结果:")
        print(f"   有效映射: {valid_cids}")
        print(f"   无效映射: {invalid_cids}")
        
        # 如果有有效映射，应该能返回具体结果而不是N/A
        if valid_cids:
            print(f"✅ 找到有效映射，应该能返回具体CID而不是N/A")
            print(f"💡 建议使用: {valid_cids[0]}")
            return True
        else:
            print(f"⚠️ 没有找到有效映射")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_smart_multiple_mapping_handler():
    """创建智能多重映射处理器"""
    print("\n\n🧠 创建智能多重映射处理器")
    print("=" * 50)
    
    handler_code = '''
def handle_multiple_mapping_smart(search_detail, code, results):
    """智能处理多重映射结果"""
    from modules.mapping_optimizer import MappingOptimizer
    
    print(f"🔀 智能处理多重映射: {code}")
    
    # 提取所有CID
    all_cids = []
    for result in results:
        cid = getattr(result, 'dmm_cid', '') or result.get('cid', '')
        if cid:
            all_cids.append(cid)
    
    if not all_cids:
        return None
    
    print(f"🎯 待验证的CID: {all_cids}")
    
    # 使用映射优化器验证
    optimizer = MappingOptimizer()
    valid_cids, invalid_cids = optimizer.optimize_mapping_list(all_cids)
    
    print(f"✅ 有效CID: {valid_cids}")
    print(f"❌ 无效CID: {invalid_cids}")
    
    if not valid_cids:
        print("⚠️ 没有有效CID，返回第一个结果")
        return search_detail._format_single_result(results[0], code)
    
    # 选择最佳CID（优先数据库中有详情的）
    best_cid = None
    for cid in valid_cids:
        try:
            from dmm_firefox_crawler import DMMFirefoxCrawler
            crawler = DMMFirefoxCrawler()
            detail_info = search_detail._search_firefox_database(code, crawler)
            
            if detail_info and detail_info.get('cid') == cid:
                print(f"🎯 选择有数据库详情的CID: {cid}")
                best_cid = cid
                break
        except:
            continue
    
    if not best_cid:
        best_cid = valid_cids[0]
        print(f"🎯 选择第一个有效CID: {best_cid}")
    
    # 找到对应的result对象
    for result in results:
        result_cid = getattr(result, 'dmm_cid', '') or result.get('cid', '')
        if result_cid == best_cid:
            formatted_result = search_detail._format_single_result(result, code)
            formatted_result["message"] = f"✅ 智能多重映射选择！番号: {code}, CID: {best_cid}"
            return formatted_result
    
    # 备用
    return search_detail._format_single_result(results[0], code)

# 使用示例:
# result = handle_multiple_mapping_smart(search_detail, "MILK-251", results)
'''
    
    print("💡 智能多重映射处理器代码已生成")
    print("📝 可以集成到SearchDetailModule中使用")
    
    return handler_code

def main():
    """主函数"""
    print("🔧 映射优化器修复测试")
    print("=" * 60)
    
    try:
        # 测试映射优化器修复
        fix_success = test_mapping_optimizer_fix()
        
        # 测试多重映射优化
        mapping_success = test_multiple_mapping_with_optimizer()
        
        # 生成智能处理器
        handler_code = create_smart_multiple_mapping_handler()
        
        print("\n" + "=" * 60)
        print("📊 修复效果总结:")
        
        if fix_success:
            print("✅ 映射优化器修复成功，能正确识别无效映射")
        else:
            print("⚠️ 映射优化器仍需调整")
        
        if mapping_success:
            print("✅ 多重映射优化可行，有有效映射可选择")
        else:
            print("⚠️ 多重映射优化需要进一步处理")
        
        print("\n💡 下一步建议:")
        print("1. 集成智能多重映射处理器")
        print("2. 修改SearchDetailModule使用优化器")
        print("3. 重新测试MILK-251搜索")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
