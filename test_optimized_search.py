#!/usr/bin/env python3
"""
测试优化后的搜索功能
"""

import sys
import time
sys.path.append('modules')

def test_milk_251_optimization():
    """测试MILK-251多重映射优化"""
    print("🧪 测试MILK-251多重映射优化")
    print("=" * 50)
    
    try:
        from modules.search_detail import SearchDetailModule
        
        search_detail = SearchDetailModule()
        
        print("🔍 搜索MILK-251...")
        start_time = time.time()
        
        result = search_detail.search_dmm_enhanced("MILK-251")
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n📊 搜索结果:")
        print(f"   耗时: {duration:.2f}秒")
        print(f"   成功: {result.get('success', False)}")
        print(f"   CID: {result.get('cid', 'N/A')}")
        print(f"   来源: {result.get('source', 'N/A')}")
        print(f"   消息: {result.get('message', 'N/A')}")
        
        # 检查是否还有详细信息
        if result.get('title'):
            print(f"   标题: {result.get('title', 'N/A')}")
        if result.get('actress'):
            print(f"   演员: {result.get('actress', 'N/A')}")
        if result.get('duration'):
            print(f"   时长: {result.get('duration', 'N/A')}")
        
        # 判断优化效果
        if result.get('cid') != 'N/A' and result.get('cid'):
            print(f"\n✅ 优化成功！不再返回N/A，返回具体CID: {result.get('cid')}")
            return True
        else:
            print(f"\n⚠️ 仍返回N/A，优化未完全生效")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_other_cases():
    """测试其他搜索案例"""
    print("\n\n🧪 测试其他搜索案例")
    print("=" * 50)
    
    try:
        from modules.search_detail import SearchDetailModule
        
        search_detail = SearchDetailModule()
        
        test_cases = [
            ("CAWD-797", "单一映射，应该快速成功"),
            ("PRED-123", "单一映射，需要实时爬取"),
            ("IPX-123", "单一映射测试")
        ]
        
        for code, description in test_cases:
            print(f"\n🧪 测试: {code} ({description})")
            
            start_time = time.time()
            result = search_detail.search_dmm_enhanced(code)
            duration = time.time() - start_time
            
            status = "✅ 成功" if result.get('success') else "❌ 失败"
            cid = result.get('cid', 'N/A')
            source = result.get('source', 'N/A')
            
            print(f"   {status} - 耗时: {duration:.2f}秒 - CID: {cid} - 来源: {source}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_404_optimization():
    """测试404优化效果"""
    print("\n\n🚀 测试404优化效果")
    print("=" * 50)
    
    try:
        from modules.search_detail import SearchDetailModule
        
        search_detail = SearchDetailModule()
        
        print("🔍 测试不存在的番号: NONEXIST-999")
        start_time = time.time()
        
        result = search_detail.search_dmm_enhanced("NONEXIST-999")
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n📊 404优化测试结果:")
        print(f"   耗时: {duration:.2f}秒")
        print(f"   结果: {'失败（预期）' if not result.get('success') else '意外成功'}")
        
        if duration < 10:
            print(f"   ✅ 404优化生效！快速失败，避免长时间等待")
            return True
        else:
            print(f"   ⚠️ 404优化可能未完全生效，耗时较长")
            return False
            
    except KeyboardInterrupt:
        print(f"\n⚠️ 测试被中断（可能仍在重试404）")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def performance_comparison():
    """性能对比测试"""
    print("\n\n⚡ 性能对比测试")
    print("=" * 50)
    
    try:
        from modules.search_detail import SearchDetailModule
        
        search_detail = SearchDetailModule()
        
        # 测试多个案例的平均性能
        test_codes = ["CAWD-797", "MILK-251", "PRED-123"]
        total_time = 0
        success_count = 0
        
        for code in test_codes:
            print(f"🔍 测试: {code}")
            
            start_time = time.time()
            result = search_detail.search_dmm_enhanced(code)
            duration = time.time() - start_time
            
            total_time += duration
            if result.get('success'):
                success_count += 1
            
            print(f"   耗时: {duration:.2f}秒 - {'成功' if result.get('success') else '失败'}")
        
        avg_time = total_time / len(test_codes)
        success_rate = success_count / len(test_codes)
        
        print(f"\n📊 性能统计:")
        print(f"   平均耗时: {avg_time:.2f}秒")
        print(f"   成功率: {success_rate:.2%}")
        print(f"   总耗时: {total_time:.2f}秒")
        
        if avg_time < 5:
            print(f"   ✅ 性能优秀！平均搜索时间小于5秒")
        elif avg_time < 10:
            print(f"   ✅ 性能良好！平均搜索时间小于10秒")
        else:
            print(f"   ⚠️ 性能需要进一步优化")
        
        return avg_time < 10
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 优化后搜索功能测试")
    print("=" * 60)
    
    try:
        # 测试MILK-251优化
        milk_success = test_milk_251_optimization()
        
        # 测试其他案例
        other_success = test_other_cases()
        
        # 测试404优化
        error_404_success = test_404_optimization()
        
        # 性能对比
        performance_success = performance_comparison()
        
        print("\n" + "=" * 60)
        print("📊 优化效果总结:")
        
        if milk_success:
            print("✅ MILK-251多重映射优化成功")
        else:
            print("⚠️ MILK-251仍需进一步优化")
        
        if other_success:
            print("✅ 其他搜索案例正常工作")
        else:
            print("⚠️ 其他搜索案例有问题")
        
        if error_404_success:
            print("✅ 404错误快速失败优化成功")
        else:
            print("⚠️ 404错误处理仍需优化")
        
        if performance_success:
            print("✅ 整体性能表现良好")
        else:
            print("⚠️ 整体性能需要进一步优化")
        
        # 总体评估
        success_count = sum([milk_success, other_success, error_404_success, performance_success])
        
        print(f"\n🎯 优化成功率: {success_count}/4 ({success_count/4:.1%})")
        
        if success_count >= 3:
            print("🎉 优化效果显著！搜索功能大幅改善")
        elif success_count >= 2:
            print("👍 优化效果良好！主要问题已解决")
        else:
            print("🔧 仍需继续优化")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
