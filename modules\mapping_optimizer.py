#!/usr/bin/env python3
"""
映射优化器 - 基于测试结果优化CID映射策略
"""

import json
import os
import sqlite3
import requests
import time
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta

class MappingOptimizer:
    """映射优化器"""
    
    def __init__(self, cache_file: str = "mapping_validity_cache.json"):
        self.cache_file = cache_file
        self.validity_cache = self._load_cache()
        self.cache_expiry_days = 7  # 缓存7天有效
    
    def _load_cache(self) -> Dict:
        """加载映射有效性缓存"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"⚠️ 加载缓存失败: {e}")
        return {}
    
    def _save_cache(self):
        """保存映射有效性缓存"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.validity_cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"⚠️ 保存缓存失败: {e}")
    
    def _is_cache_valid(self, cid: str) -> bool:
        """检查缓存是否有效"""
        if cid not in self.validity_cache:
            return False
        
        cache_entry = self.validity_cache[cid]
        if 'timestamp' not in cache_entry:
            return False
        
        cache_time = datetime.fromisoformat(cache_entry['timestamp'])
        expiry_time = cache_time + timedelta(days=self.cache_expiry_days)
        
        return datetime.now() < expiry_time
    
    def check_cid_validity(self, cid: str, db_file: str = "dmm_firefox_database.db") -> Dict:
        """
        检查CID有效性
        返回: {"valid": bool, "source": str, "cached": bool}
        """
        # 检查缓存
        if self._is_cache_valid(cid):
            cache_entry = self.validity_cache[cid]
            return {
                "valid": cache_entry["valid"],
                "source": cache_entry["source"],
                "cached": True,
                "timestamp": cache_entry["timestamp"]
            }
        
        # 1. 首先检查本地数据库
        if os.path.exists(db_file):
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM dmm_works WHERE cid = ?", (cid,))
                count = cursor.fetchone()[0]
                conn.close()
                
                if count > 0:
                    result = {"valid": True, "source": "local_database", "cached": False}
                    self._cache_result(cid, result)
                    return result
            except Exception as e:
                print(f"⚠️ 数据库检查失败: {e}")
        
        # 2. 在线快速验证（HEAD请求）
        try:
            url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={cid}/"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.head(url, headers=headers, timeout=10, allow_redirects=True)

            # 检查最终URL，如果重定向到错误页面则认为无效
            final_url = response.url if hasattr(response, 'url') else url

            if response.status_code == 200 and 'error' not in final_url.lower() and 'notfound' not in final_url.lower():
                # 进一步验证：检查是否重定向到了通用错误页面
                if final_url != url and len(final_url.split('/')) < len(url.split('/')):
                    result = {"valid": False, "source": "redirect_to_error", "cached": False}
                else:
                    result = {"valid": True, "source": "online_verification", "cached": False}
            elif response.status_code == 302 or response.status_code == 301:
                # 重定向可能是到错误页面
                result = {"valid": False, "source": f"redirect_{response.status_code}", "cached": False}
            else:
                result = {"valid": False, "source": f"online_error_{response.status_code}", "cached": False}
            
            self._cache_result(cid, result)
            return result
            
        except requests.exceptions.Timeout:
            result = {"valid": False, "source": "timeout", "cached": False}
            self._cache_result(cid, result)
            return result
        except Exception as e:
            result = {"valid": False, "source": f"network_error", "cached": False}
            self._cache_result(cid, result)
            return result
    
    def _cache_result(self, cid: str, result: Dict):
        """缓存检查结果"""
        cache_entry = {
            "valid": result["valid"],
            "source": result["source"],
            "timestamp": datetime.now().isoformat()
        }
        self.validity_cache[cid] = cache_entry
        self._save_cache()
    
    def optimize_mapping_list(self, cid_list: List[str]) -> Tuple[List[str], List[str]]:
        """
        优化映射列表，返回有效和无效的CID列表
        返回: (valid_cids, invalid_cids)
        """
        valid_cids = []
        invalid_cids = []
        
        print(f"🔍 检查 {len(cid_list)} 个映射的有效性...")
        
        for i, cid in enumerate(cid_list, 1):
            print(f"   检查 {i}/{len(cid_list)}: {cid}")
            
            validity = self.check_cid_validity(cid)
            
            if validity["valid"]:
                valid_cids.append(cid)
                cache_info = " (缓存)" if validity["cached"] else ""
                print(f"      ✅ 有效 - {validity['source']}{cache_info}")
            else:
                invalid_cids.append(cid)
                cache_info = " (缓存)" if validity["cached"] else ""
                print(f"      ❌ 无效 - {validity['source']}{cache_info}")
            
            # 避免请求过于频繁
            if not validity["cached"] and i < len(cid_list):
                time.sleep(0.5)
        
        return valid_cids, invalid_cids
    
    def get_cache_stats(self) -> Dict:
        """获取缓存统计信息"""
        total_entries = len(self.validity_cache)
        valid_entries = sum(1 for entry in self.validity_cache.values() if entry.get("valid", False))
        invalid_entries = total_entries - valid_entries
        
        # 统计来源
        sources = {}
        for entry in self.validity_cache.values():
            source = entry.get("source", "unknown")
            sources[source] = sources.get(source, 0) + 1
        
        return {
            "total_entries": total_entries,
            "valid_entries": valid_entries,
            "invalid_entries": invalid_entries,
            "validity_rate": valid_entries / total_entries if total_entries > 0 else 0,
            "sources": sources
        }
    
    def clean_expired_cache(self):
        """清理过期缓存"""
        current_time = datetime.now()
        expired_keys = []
        
        for cid, entry in self.validity_cache.items():
            if 'timestamp' in entry:
                cache_time = datetime.fromisoformat(entry['timestamp'])
                expiry_time = cache_time + timedelta(days=self.cache_expiry_days)
                
                if current_time >= expiry_time:
                    expired_keys.append(cid)
        
        for key in expired_keys:
            del self.validity_cache[key]
        
        if expired_keys:
            self._save_cache()
            print(f"🧹 清理了 {len(expired_keys)} 个过期缓存条目")
        
        return len(expired_keys)
    
    def suggest_mapping_improvements(self, studio: str, all_mappings: List[str]) -> Dict:
        """为特定厂商建议映射改进"""
        valid_mappings, invalid_mappings = self.optimize_mapping_list(all_mappings)
        
        suggestions = {
            "studio": studio,
            "total_mappings": len(all_mappings),
            "valid_mappings": valid_mappings,
            "invalid_mappings": invalid_mappings,
            "validity_rate": len(valid_mappings) / len(all_mappings) if all_mappings else 0,
            "suggestions": []
        }
        
        # 生成建议
        if len(valid_mappings) == 0:
            suggestions["suggestions"].append("⚠️ 所有映射都无效，需要重新检查厂商映射规则")
        elif len(invalid_mappings) > 0:
            suggestions["suggestions"].append(f"💡 移除 {len(invalid_mappings)} 个无效映射以提升搜索效率")
        
        if len(valid_mappings) > 1:
            suggestions["suggestions"].append("🔀 多个有效映射，建议按成功率排序")
        
        return suggestions

def test_mapping_optimizer():
    """测试映射优化器"""
    print("🧪 测试映射优化器")
    print("=" * 50)
    
    optimizer = MappingOptimizer()
    
    # 测试已知的映射
    test_mappings = {
        "MILK-251": ["h_1240milk00251", "milk00251"],
        "CAWD-797": ["cawd00797"],
        "PRED-123": ["pred00123"]
    }
    
    for studio_code, mappings in test_mappings.items():
        print(f"\n🧪 测试 {studio_code}:")
        studio = studio_code.split('-')[0]
        
        suggestions = optimizer.suggest_mapping_improvements(studio, mappings)
        
        print(f"   总映射数: {suggestions['total_mappings']}")
        print(f"   有效映射: {suggestions['valid_mappings']}")
        print(f"   无效映射: {suggestions['invalid_mappings']}")
        print(f"   有效率: {suggestions['validity_rate']:.2%}")
        
        for suggestion in suggestions['suggestions']:
            print(f"   {suggestion}")
    
    # 显示缓存统计
    stats = optimizer.get_cache_stats()
    print(f"\n📊 缓存统计:")
    print(f"   总条目: {stats['total_entries']}")
    print(f"   有效条目: {stats['valid_entries']}")
    print(f"   无效条目: {stats['invalid_entries']}")
    print(f"   有效率: {stats['validity_rate']:.2%}")

if __name__ == "__main__":
    test_mapping_optimizer()
