#!/usr/bin/env python3
"""
调试多重映射功能
"""

import sys
import os

print("🔧 开始调试多重映射功能...")

try:
    # 添加模块路径
    sys.path.append('modules')
    print("✅ 模块路径添加成功")
    
    # 导入搜索模块
    from modules.search_detail import SearchDetailModule
    print("✅ SearchDetailModule 导入成功")
    
    # 创建搜索实例
    search_detail = SearchDetailModule()
    print("✅ SearchDetailModule 实例创建成功")
    
    # 测试CID生成
    test_code = "MILK-251"
    print(f"\n🧪 测试番号: {test_code}")
    
    # 生成可能的CID
    possible_cids = search_detail._generate_possible_cids(test_code)
    print(f"🎯 生成的CID: {possible_cids}")
    print(f"📊 CID数量: {len(possible_cids)}")
    
    # 检查FastDMM是否可用
    if hasattr(search_detail, 'fast_search') and search_detail.fast_search:
        print("✅ FastDMM搜索引擎可用")
        
        if hasattr(search_detail.fast_search, 'mapping_manager') and search_detail.fast_search.mapping_manager:
            print("✅ 映射管理器可用")
            
            # 获取MILK的映射信息
            mapping_info = search_detail.fast_search.mapping_manager.get_mapping_info('MILK')
            print(f"🔍 MILK映射信息: {mapping_info}")
        else:
            print("❌ 映射管理器不可用")
    else:
        print("❌ FastDMM搜索引擎不可用")
    
    print("\n✅ 调试完成")
    
except Exception as e:
    print(f"❌ 调试过程中出错: {e}")
    import traceback
    traceback.print_exc()
