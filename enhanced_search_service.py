#!/usr/bin/env python3
"""
增强搜索服务 - 集成DMM Firefox数据库
优先使用完整详情数据，回退到现有搜索系统
"""

import sqlite3
import re
import os
from typing import Optional, Dict, List
from pathlib import Path

class EnhancedSearchService:
    """增强搜索服务 - 集成多数据源"""
    
    def __init__(self, 
                 dmm_firefox_db: str = "dmm_firefox_database.db",
                 fast_dmm_db: str = "data/fast_dmm.db"):
        self.dmm_firefox_db = dmm_firefox_db
        self.fast_dmm_db = fast_dmm_db
        
        # 检查数据库可用性
        self.dmm_firefox_available = os.path.exists(dmm_firefox_db)
        self.fast_dmm_available = os.path.exists(fast_dmm_db)
        
        print(f"📊 数据源状态:")
        print(f"  DMM Firefox DB: {'✅' if self.dmm_firefox_available else '❌'}")
        print(f"  Fast DMM DB: {'✅' if self.fast_dmm_available else '❌'}")
    
    def search_by_number(self, number: str) -> Optional[Dict]:
        """根据番号搜索（主要接口）"""
        print(f"🔍 搜索番号: {number}")
        
        # 1. 优先使用DMM Firefox数据库（最完整）
        if self.dmm_firefox_available:
            result = self._search_dmm_firefox_by_number(number)
            if result:
                print("✅ 在DMM Firefox数据库中找到")
                return result
        
        # 2. 尝试通过CID搜索
        cid = self._number_to_cid(number)
        if cid:
            result = self.search_by_cid(cid)
            if result:
                return result
        
        # 3. 回退到现有搜索系统
        print("🔄 回退到现有搜索系统")
        return self._fallback_search(number)
    
    def search_by_cid(self, cid: str) -> Optional[Dict]:
        """根据CID搜索"""
        print(f"🔍 搜索CID: {cid}")
        
        if self.dmm_firefox_available:
            result = self._search_dmm_firefox_by_cid(cid)
            if result:
                print("✅ 在DMM Firefox数据库中找到")
                return result
        
        return None
    
    def _search_dmm_firefox_by_number(self, number: str) -> Optional[Dict]:
        """在DMM Firefox数据库中按番号搜索"""
        try:
            conn = sqlite3.connect(self.dmm_firefox_db)
            cursor = conn.cursor()
            
            # 标准化番号格式
            normalized_number = number.upper().replace(" ", "").replace("_", "-")
            
            # 多种匹配策略
            queries = [
                "SELECT * FROM dmm_works WHERE number = ? LIMIT 1",
                "SELECT * FROM dmm_works WHERE number LIKE ? LIMIT 1", 
                "SELECT * FROM dmm_works WHERE UPPER(REPLACE(number, ' ', '')) = ? LIMIT 1"
            ]
            
            for query in queries:
                if "LIKE" in query:
                    cursor.execute(query, (f"%{normalized_number}%",))
                else:
                    cursor.execute(query, (normalized_number,))
                
                row = cursor.fetchone()
                if row:
                    conn.close()
                    return self._format_dmm_firefox_result(row)
            
            conn.close()
            return None
            
        except Exception as e:
            print(f"❌ DMM Firefox数据库搜索失败: {e}")
            return None
    
    def _search_dmm_firefox_by_cid(self, cid: str) -> Optional[Dict]:
        """在DMM Firefox数据库中按CID搜索"""
        try:
            conn = sqlite3.connect(self.dmm_firefox_db)
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM dmm_works WHERE cid = ? LIMIT 1", (cid,))
            row = cursor.fetchone()
            
            conn.close()
            
            if row:
                return self._format_dmm_firefox_result(row)
            return None
            
        except Exception as e:
            print(f"❌ DMM Firefox数据库搜索失败: {e}")
            return None
    
    def _format_dmm_firefox_result(self, row) -> Dict:
        """格式化DMM Firefox数据库结果"""
        # 假设列顺序：id, cid, title, number, studio, detail_url, page_number, crawl_time, 
        # release_date, sale_date, duration, actress, director, series, maker, label, genre, rating, detail_crawled, detail_crawl_time
        
        return {
            "success": True,
            "source": "dmm_firefox",
            "cid": row[1] if len(row) > 1 else "",
            "title": row[2] if len(row) > 2 else "",
            "number": row[3] if len(row) > 3 else "",
            "studio": row[4] if len(row) > 4 else "",
            "url": row[5] if len(row) > 5 else "",
            "actress": row[11] if len(row) > 11 else "",
            "duration": row[10] if len(row) > 10 else "",
            "maker": row[14] if len(row) > 14 else "",
            "rating": row[17] if len(row) > 17 else "",
            "release_date": row[8] if len(row) > 8 else "",
            "genre": row[16] if len(row) > 16 else "",
            "director": row[12] if len(row) > 12 else "",
            "series": row[13] if len(row) > 13 else "",
            "label": row[15] if len(row) > 15 else "",
            "message": "✅ 从DMM Firefox数据库获取完整信息"
        }
    
    def _number_to_cid(self, number: str) -> Optional[str]:
        """番号转CID（简化版）"""
        # 移除连字符，转小写
        cid = number.replace("-", "").lower()
        
        # 基本格式检查
        if re.match(r'^[a-z]+\d+$', cid):
            return cid
        
        return None
    
    def _fallback_search(self, number: str) -> Optional[Dict]:
        """回退搜索（使用现有系统）"""
        try:
            # 这里可以集成现有的search_detail模块
            from modules.search_detail import SearchDetail
            
            search_detail = SearchDetail()
            result = search_detail.search_dmm_enhanced(number)
            
            if result and result.get("success"):
                result["source"] = "fallback_search"
                return result
            
        except Exception as e:
            print(f"❌ 回退搜索失败: {e}")
        
        return {
            "success": False,
            "source": "none",
            "message": f"❌ 未找到番号 {number} 的信息"
        }
    
    def batch_search(self, numbers: List[str]) -> Dict[str, Dict]:
        """批量搜索"""
        results = {}
        
        for number in numbers:
            result = self.search_by_number(number)
            results[number] = result
        
        return results
    
    def get_statistics(self) -> Dict:
        """获取数据库统计信息"""
        stats = {}
        
        if self.dmm_firefox_available:
            try:
                conn = sqlite3.connect(self.dmm_firefox_db)
                cursor = conn.cursor()
                
                cursor.execute("SELECT COUNT(*) FROM dmm_works")
                total_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM dmm_works WHERE title IS NOT NULL AND title != ''")
                title_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM dmm_works WHERE detail_crawled = 1")
                detailed_count = cursor.fetchone()[0]
                
                stats["dmm_firefox"] = {
                    "total_works": total_count,
                    "with_title": title_count,
                    "detailed": detailed_count,
                    "title_rate": f"{title_count/total_count*100:.1f}%" if total_count > 0 else "0%"
                }
                
                conn.close()
                
            except Exception as e:
                stats["dmm_firefox"] = {"error": str(e)}
        
        return stats

# 使用示例
if __name__ == "__main__":
    # 创建增强搜索服务
    search_service = EnhancedSearchService()
    
    # 获取统计信息
    stats = search_service.get_statistics()
    print("📊 数据库统计:")
    for db_name, db_stats in stats.items():
        print(f"  {db_name}: {db_stats}")
    
    # 测试搜索
    test_numbers = ["CAWD-797", "MILK-251"]
    
    for number in test_numbers:
        print(f"\n🧪 测试搜索: {number}")
        result = search_service.search_by_number(number)
        if result and result.get("success"):
            print(f"  ✅ 找到: {result.get('title', 'N/A')}")
            print(f"  📍 CID: {result.get('cid', 'N/A')}")
            print(f"  🎬 演员: {result.get('actress', 'N/A')}")
        else:
            print(f"  ❌ 未找到")
