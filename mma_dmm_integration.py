#!/usr/bin/env python3
"""
MMA-DMM集成接口
统一的搜索和重命名服务接口
"""

import os
from typing import List, Dict, Optional
from enhanced_search_service import EnhancedSearchService
from enhanced_rename_service import EnhancedRenameService
from crawler_integrated_search import CrawlerIntegratedSearch

class MMADMMIntegration:
    """MMA-DMM集成服务"""
    
    def __init__(self,
                 dmm_firefox_db: str = "dmm_firefox_database.db",
                 fast_dmm_db: str = "data/fast_dmm.db",
                 enable_realtime_crawl: bool = True):
        """
        初始化集成服务

        Args:
            dmm_firefox_db: DMM Firefox数据库路径
            fast_dmm_db: Fast DMM数据库路径
            enable_realtime_crawl: 是否启用实时爬取
        """
        self.search_service = EnhancedSearchService(dmm_firefox_db, fast_dmm_db)
        self.rename_service = EnhancedRenameService()
        self.crawler_search = CrawlerIntegratedSearch(dmm_firefox_db, enable_realtime_crawl)

        print("🚀 MMA-DMM集成服务已启动")
        print(f"🕷️ 实时爬取: {'✅ 启用' if enable_realtime_crawl else '❌ 禁用'}")
        self._print_status()
    
    def _print_status(self):
        """打印服务状态"""
        stats = self.search_service.get_statistics()
        print("📊 服务状态:")
        
        if "dmm_firefox" in stats:
            dmm_stats = stats["dmm_firefox"]
            if "error" not in dmm_stats:
                print(f"  ✅ DMM Firefox数据库: {dmm_stats['total_works']} 作品")
                print(f"     📝 标题完整率: {dmm_stats['title_rate']}")
                print(f"     🔍 详情完整率: {dmm_stats['detailed']} 作品")
            else:
                print(f"  ❌ DMM Firefox数据库: {dmm_stats['error']}")
        else:
            print("  ❌ DMM Firefox数据库: 不可用")
    
    # ==================== 搜索接口 ====================

    def search_work_info(self, query: str, search_type: str = "auto", enable_crawl: bool = True) -> Dict:
        """
        搜索作品信息（支持实时爬取）

        Args:
            query: 搜索查询（番号或CID）
            search_type: 搜索类型 ("auto", "number", "cid")
            enable_crawl: 是否启用实时爬取

        Returns:
            搜索结果字典
        """
        # 如果启用爬取，优先使用爬虫集成搜索
        if enable_crawl:
            return self.crawler_search.search_with_crawl(query)

        # 否则使用传统搜索
        if search_type == "cid":
            return self.search_service.search_by_cid(query)
        elif search_type == "number":
            return self.search_service.search_by_number(query)
        else:
            # 自动判断类型
            if len(query) > 6 and query.islower() and query.isalnum():
                # 可能是CID
                result = self.search_service.search_by_cid(query)
                if result and result.get("success"):
                    return result

            # 尝试作为番号搜索
            return self.search_service.search_by_number(query)
    
    def get_work_details(self, identifier: str) -> Optional[Dict]:
        """
        获取作品详细信息
        
        Args:
            identifier: 番号或CID
        
        Returns:
            详细信息字典
        """
        return self.search_work_info(identifier, "auto")
    
    def manual_search_with_crawl(self, query: str) -> Dict:
        """
        手动搜索（强制实时爬取）

        Args:
            query: 搜索查询

        Returns:
            搜索结果字典
        """
        return self.crawler_search.manual_search_and_crawl(query)

    def batch_search_works(self, identifiers: List[str], enable_crawl: bool = True, max_crawl: int = 10) -> Dict[str, Dict]:
        """
        批量搜索作品信息

        Args:
            identifiers: 番号或CID列表
            enable_crawl: 是否启用实时爬取
            max_crawl: 最大爬取数量

        Returns:
            搜索结果字典
        """
        if enable_crawl:
            return self.crawler_search.batch_search_with_crawl(identifiers, max_crawl)
        else:
            return self.search_service.batch_search(identifiers)
    
    # ==================== 重命名接口 ====================
    
    def preview_rename(self, file_paths: List[str]) -> List[Dict]:
        """
        预览重命名结果
        
        Args:
            file_paths: 文件路径列表
        
        Returns:
            预览结果列表
        """
        return self.rename_service.preview_rename(file_paths)
    
    def batch_rename(self, file_paths: List[str], dry_run: bool = True) -> Dict:
        """
        批量重命名文件
        
        Args:
            file_paths: 文件路径列表
            dry_run: 是否为预览模式
        
        Returns:
            重命名结果字典
        """
        return self.rename_service.batch_rename(file_paths, dry_run)
    
    def rename_directory(self, directory: str, dry_run: bool = True) -> Dict:
        """
        重命名目录下的所有MP4文件
        
        Args:
            directory: 目录路径
            dry_run: 是否为预览模式
        
        Returns:
            重命名结果字典
        """
        if not os.path.exists(directory):
            return {
                "success": False,
                "error": f"目录不存在: {directory}"
            }
        
        # 查找所有MP4文件
        mp4_files = []
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.lower().endswith('.mp4'):
                    mp4_files.append(os.path.join(root, file))
        
        if not mp4_files:
            return {
                "success": True,
                "message": "目录中没有找到MP4文件",
                "results": []
            }
        
        print(f"📁 在目录 {directory} 中找到 {len(mp4_files)} 个MP4文件")
        return self.batch_rename(mp4_files, dry_run)
    
    # ==================== 工具接口 ====================
    
    def extract_number_from_filename(self, filename: str) -> Optional[str]:
        """从文件名提取番号"""
        return self.rename_service.extract_number_from_filename(filename)
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        return self.search_service.get_statistics()
    
    def health_check(self) -> Dict:
        """健康检查"""
        stats = self.get_statistics()
        
        health = {
            "status": "healthy",
            "services": {
                "search": True,
                "rename": True
            },
            "data_sources": {}
        }
        
        # 检查数据源
        if "dmm_firefox" in stats:
            if "error" not in stats["dmm_firefox"]:
                health["data_sources"]["dmm_firefox"] = {
                    "status": "available",
                    "works": stats["dmm_firefox"]["total_works"]
                }
            else:
                health["data_sources"]["dmm_firefox"] = {
                    "status": "error",
                    "error": stats["dmm_firefox"]["error"]
                }
                health["status"] = "degraded"
        else:
            health["data_sources"]["dmm_firefox"] = {
                "status": "unavailable"
            }
            health["status"] = "degraded"
        
        return health

# 使用示例和测试
if __name__ == "__main__":
    # 创建集成服务
    integration = MMADMMIntegration()
    
    # 健康检查
    health = integration.health_check()
    print(f"\n🏥 健康检查: {health['status']}")
    
    # 测试搜索
    print("\n🔍 测试搜索功能:")
    test_queries = ["CAWD-797", "cawd00797", "MILK-251"]
    
    for query in test_queries:
        print(f"\n🧪 搜索: {query}")
        result = integration.search_work_info(query)
        
        if result and result.get("success"):
            print(f"  ✅ 找到: {result.get('title', 'N/A')}")
            print(f"  📍 CID: {result.get('cid', 'N/A')}")
            print(f"  🎬 演员: {result.get('actress', 'N/A')}")
            print(f"  📊 来源: {result.get('source', 'N/A')}")
        else:
            print(f"  ❌ 未找到")
    
    # 测试重命名预览
    print("\n📝 测试重命名预览:")
    test_files = [
        "CAWD-797.mp4",
        "CAWD-797-4K.mp4"
    ]
    
    preview = integration.preview_rename(test_files)
    for result in preview:
        print(f"  📄 {result['original']} → {result['new']}")
        print(f"     状态: {result['status']}, 原因: {result['reason']}")
    
    print("\n✅ 测试完成！")
