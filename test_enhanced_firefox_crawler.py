#!/usr/bin/env python3
"""
测试增强版Firefox爬虫
"""
import sqlite3
from dmm_firefox_crawler import DMMFirefoxCrawler

def test_single_work():
    """测试单个作品的详情提取"""
    print("🧪 测试单个作品详情提取")
    print("=" * 50)
    
    crawler = DMMFirefoxCrawler("test_enhanced_dmm.db")
    
    # 测试一个已知的CID
    test_cid = "cawd00797"
    test_url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={test_cid}/"
    
    print(f"测试CID: {test_cid}")
    print(f"测试URL: {test_url}")
    
    detail_info = crawler.extract_detail_info(test_url, test_cid)
    
    if detail_info:
        print(f"\n✅ 成功提取详情信息:")
        for key, value in detail_info.items():
            print(f"   {key}: {value}")
    else:
        print(f"\n❌ 详情信息提取失败")

def test_database_structure():
    """测试数据库结构"""
    print("\n🗄️ 测试数据库结构")
    print("=" * 50)
    
    crawler = DMMFirefoxCrawler("test_enhanced_dmm.db")
    
    try:
        conn = sqlite3.connect("test_enhanced_dmm.db")
        cursor = conn.cursor()
        
        cursor.execute("PRAGMA table_info(dmm_works)")
        columns = cursor.fetchall()
        
        print("数据库字段:")
        for i, (cid, name, data_type, not_null, default_value, pk) in enumerate(columns, 1):
            print(f"   {i:2d}. {name} ({data_type})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库结构检查失败: {e}")

def view_test_results():
    """查看测试结果"""
    print("\n📊 查看测试结果")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect("test_enhanced_dmm.db")
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM dmm_works")
        total_count = cursor.fetchone()[0]
        print(f"总记录数: {total_count}")
        
        if total_count > 0:
            cursor.execute('''
                SELECT cid, title, actress, duration, maker, rating, detail_crawled 
                FROM dmm_works 
                LIMIT 5
            ''')
            
            records = cursor.fetchall()
            
            print(f"\n前{len(records)}条记录:")
            for i, (cid, title, actress, duration, maker, rating, detail_crawled) in enumerate(records, 1):
                print(f"\n{i}. CID: {cid}")
                print(f"   标题: {title or '未提取'}")
                print(f"   演员: {actress or '未提取'}")
                print(f"   时长: {duration or '未提取'}")
                print(f"   制作商: {maker or '未提取'}")
                print(f"   评分: {rating or '未提取'}")
                print(f"   详情已爬取: {'是' if detail_crawled == 1 else '否'}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 查看结果失败: {e}")

def main():
    """主函数"""
    print("🧪 增强版Firefox爬虫测试工具")
    print("=" * 60)
    
    print("\n📋 测试选项:")
    print("   1. 测试单个作品详情提取")
    print("   2. 测试数据库结构")
    print("   3. 运行第一页爬取测试")
    print("   4. 查看测试结果")
    
    choice = input("\n请选择测试项目 (1-4): ").strip()
    
    if choice == '1':
        test_single_work()
    
    elif choice == '2':
        test_database_structure()
    
    elif choice == '3':
        print("🚀 开始第一页爬取测试...")
        print("⚠️  这可能需要5-10分钟")
        confirm = input("确认继续？(y/N): ").strip().lower()
        if confirm == 'y':
            crawler = DMMFirefoxCrawler("test_enhanced_dmm.db")
            crawler.crawl_first_page()
        else:
            print("测试已取消")
    
    elif choice == '4':
        view_test_results()
    
    else:
        print("❌ 无效选择")
        return
    
    print(f"\n✅ 测试完成!")

if __name__ == "__main__":
    main()
