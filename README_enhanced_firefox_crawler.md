# DMM Firefox增强爬虫

## 功能特点

✅ **直接获取完整详情信息** - 不需要后续脚本补充详情
✅ **标题提取修复** - 解决了标题为空的问题  
✅ **完整字段支持** - 包含标题、演员、时长、制作商、评分等所有信息
✅ **Firefox浏览器** - 避免Chrome渲染器问题
✅ **智能反爬策略** - 页面间隔30秒，详情页间隔2秒

## 数据库字段

增强版爬虫会直接填充以下所有字段：

- `cid` - 作品CID
- `title` - 作品标题 ⭐ **新增完整支持**
- `number` - 番号（如CAWD-797）
- `studio` - 厂商前缀（如CAWD）
- `detail_url` - 详情页URL
- `actress` - 演员信息 ⭐ **直接获取**
- `duration` - 时长信息 ⭐ **直接获取**
- `maker` - 制作商 ⭐ **直接获取**
- `rating` - 评分 ⭐ **直接获取**
- `release_date` - 发布日期
- `genre` - 类型标签
- `director` - 监督
- `series` - 系列
- `label` - 厂牌
- `detail_crawled` - 详情爬取标记（固定为1）

## 使用方法

### 1. 测试单页（推荐先测试）

```bash
python3 dmm_firefox_crawler.py
# 选择选项 1
```

### 2. 运行测试工具

```bash
python3 test_enhanced_firefox_crawler.py
```

### 3. 查看数据

```bash
python3 view_dmm_data.py
```

## 时间估算

由于需要访问每个作品的详情页：

- **单页测试**: 约5-10分钟（120个作品）
- **5页爬取**: 约25-50分钟（600个作品）
- **10页爬取**: 约50-100分钟（1200个作品）

## 与原版对比

| 功能 | 原版Firefox爬虫 | 增强版Firefox爬虫 |
|------|----------------|------------------|
| 标题提取 | ❌ 失败 | ✅ 成功 |
| 详情信息 | ❌ 需要后续脚本 | ✅ 直接获取 |
| 数据完整性 | 🔸 部分 | ✅ 完整 |
| 爬取速度 | 🚀 快 | 🐌 慢但完整 |
| 后续处理 | ❌ 需要 | ✅ 不需要 |

## 注意事项

1. **时间较长**: 由于需要访问详情页，爬取时间比原版长很多
2. **网络稳定**: 需要稳定的网络连接
3. **反爬策略**: 已内置延迟，但仍需注意不要过于频繁
4. **Firefox依赖**: 需要安装Firefox和geckodriver

## 故障排除

### 如果标题仍然为空
1. 检查网络连接
2. 查看是否被反爬限制
3. 增加延迟时间

### 如果详情页访问失败
1. 检查Cookie设置
2. 确认年龄验证处理
3. 查看错误日志

## 建议使用流程

1. **先测试**: 运行测试工具验证功能
2. **小批量**: 先爬取1-2页测试效果
3. **查看结果**: 确认数据质量后再大批量爬取
4. **定期检查**: 监控爬取进度和数据质量
