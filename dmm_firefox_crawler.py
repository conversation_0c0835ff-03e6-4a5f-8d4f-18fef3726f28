#!/usr/bin/env python3
"""
DMM Firefox爬虫 - 增强版，直接爬取详情页内容
"""
import sqlite3
import time
import re
import requests
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.firefox.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup
import logging

class DMMFirefoxCrawler:
    """DMM Firefox爬虫"""
    
    def __init__(self, db_file: str = "dmm_firefox_database.db"):
        self.db_file = db_file
        self.driver = None
        
        # 简单配置
        self.config = {
            'page_load_timeout': 60,  # 增加超时时间
        }
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        print("📊 初始化数据库...")

        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dmm_works (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cid TEXT UNIQUE NOT NULL,
                    title TEXT,
                    number TEXT,
                    studio TEXT,
                    detail_url TEXT,
                    page_number INTEGER,
                    crawl_time TEXT,
                    release_date TEXT,
                    sale_date TEXT,
                    duration TEXT,
                    actress TEXT,
                    director TEXT,
                    series TEXT,
                    maker TEXT,
                    label TEXT,
                    genre TEXT,
                    rating TEXT,
                    detail_crawled INTEGER DEFAULT 1,
                    detail_crawl_time TEXT,
                    UNIQUE(cid)
                )
            ''')

            conn.commit()
            conn.close()
            print("✅ 数据库初始化完成")

        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            raise
    
    def init_driver(self):
        """初始化Firefox浏览器"""
        print("🦊 初始化Firefox浏览器...")
        
        try:
            firefox_options = Options()
            firefox_options.add_argument('--headless')
            firefox_options.add_argument('--no-sandbox')
            firefox_options.add_argument('--disable-dev-shm-usage')
            
            self.driver = webdriver.Firefox(options=firefox_options)
            self.driver.set_page_load_timeout(self.config['page_load_timeout'])
            
            print("✅ Firefox浏览器初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ Firefox浏览器初始化失败: {e}")
            print("💡 请确保已安装Firefox和geckodriver")
            return False
    
    def extract_cid_from_url(self, url: str) -> Optional[str]:
        """从URL中提取CID"""
        try:
            cid_match = re.search(r'cid=([^&/]+)', url)
            if cid_match:
                return cid_match.group(1)
            return None
        except:
            return None

    def extract_detail_info(self, detail_url: str, cid: str, max_retries: int = 5) -> Optional[Dict]:
        """从详情页提取详细信息（带重试机制）"""

        for attempt in range(max_retries):
            try:
                # 重试延迟（优化：减少等待时间）
                if attempt > 0:
                    wait_time = attempt * 3  # 第1次重试等3秒，第2次等6秒
                    print(f"      � 第{attempt}次重试，等待{wait_time}秒...")
                    time.sleep(wait_time)

                print(f"      �🔍 访问详情页: {detail_url} (尝试{attempt+1}/{max_retries})")

                # 使用requests获取页面内容（比selenium更快）
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Cookie': 'age_check_done=1; cklg=ja'
                }

                # 优化超时时间，提升速度
                response = requests.get(detail_url, headers=headers, timeout=20)

                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')

                    detail_info = {}

                    # 提取标题
                    title = self.extract_title(soup)
                    if title:
                        detail_info['title'] = title

                    # 提取配信開始日
                    release_date = self.extract_field(soup, ['配信開始日', '配信日'])
                    if release_date:
                        detail_info['release_date'] = release_date

                    # 提取商品発売日
                    sale_date = self.extract_field(soup, ['商品発売日', '発売日'])
                    if sale_date:
                        detail_info['sale_date'] = sale_date

                    # 提取収録時間
                    duration = self.extract_field(soup, ['収録時間', '時間'])
                    if duration:
                        detail_info['duration'] = duration

                    # 提取出演者
                    actress = self.extract_field(soup, ['出演者'])
                    if actress:
                        detail_info['actress'] = actress

                    # 提取監督
                    director = self.extract_field(soup, ['監督'])
                    if director:
                        detail_info['director'] = director

                    # 提取シリーズ
                    series = self.extract_field(soup, ['シリーズ'])
                    if series:
                        detail_info['series'] = series

                    # 提取メーカー
                    maker = self.extract_field(soup, ['メーカー'])
                    if maker:
                        detail_info['maker'] = maker

                    # 提取レーベル
                    label = self.extract_field(soup, ['レーベル'])
                    if label:
                        detail_info['label'] = label

                    # 提取ジャンル
                    genre = self.extract_genre(soup)
                    if genre:
                        detail_info['genre'] = genre

                    # 提取平均評価
                    rating = self.extract_rating(soup)
                    if rating:
                        detail_info['rating'] = rating

                    # 添加CID字段
                    detail_info['cid'] = cid
                    detail_info['detail_url'] = detail_url

                    # 从CID生成番号和厂商
                    if cid:
                        number = self.generate_number_from_cid(cid)
                        if number:
                            detail_info['number'] = number
                            # 从番号提取厂商前缀
                            studio_match = re.match(r'^([A-Z]+)', number)
                            if studio_match:
                                detail_info['studio'] = studio_match.group(1)

                    # 添加爬取标记
                    detail_info['detail_crawled'] = 1
                    detail_info['detail_crawl_time'] = datetime.now().isoformat()

                    print(f"      ✅ 提取到 {len(detail_info)} 个字段")
                    return detail_info

                elif response.status_code == 404:  # Not Found - 不重试
                    print(f"      ❌ 页面不存在 (404)，跳过重试")
                    return None

                elif response.status_code == 429:  # Too Many Requests
                    print(f"      ⚠️ 请求过于频繁 (429)，尝试{attempt+1}/{max_retries}")
                    if attempt < max_retries - 1:
                        continue  # 重试
                    else:
                        return None

                else:
                    print(f"      ❌ 详情页访问失败: {response.status_code}")
                    if attempt < max_retries - 1:
                        continue  # 重试
                    else:
                        return None

            except requests.exceptions.Timeout:
                print(f"      ⏰ 连接超时，尝试{attempt+1}/{max_retries}")
                if attempt < max_retries - 1:
                    continue  # 重试
                else:
                    return None

            except requests.exceptions.ConnectionError:
                print(f"      🔌 连接错误，尝试{attempt+1}/{max_retries}")
                if attempt < max_retries - 1:
                    continue  # 重试
                else:
                    return None

            except Exception as e:
                print(f"      ❌ 详情页解析异常 (尝试{attempt+1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    continue  # 重试
                else:
                    return None

        # 所有重试都失败了
        return None

    def extract_title(self, soup: BeautifulSoup) -> Optional[str]:
        """提取标题"""
        try:
            # 方法1: 从页面标题提取
            title_tag = soup.find('title')
            if title_tag:
                title_text = title_tag.get_text().strip()
                # 移除DMM相关的后缀
                title_text = re.sub(r'\s*-\s*DMM.*$', '', title_text)
                title_text = re.sub(r'\s*\|\s*DMM.*$', '', title_text)
                if title_text and len(title_text) > 5:
                    print(f"         ✅ 页面标题提取: {title_text}")
                    return title_text

            # 方法2: 查找h1标签
            h1_tag = soup.find('h1')
            if h1_tag:
                title_text = h1_tag.get_text().strip()
                if title_text and len(title_text) > 5:
                    print(f"         ✅ H1标签提取: {title_text}")
                    return title_text

            # 方法3: 查找商品名称相关的元素
            title_selectors = [
                '.productTitle',
                '.product-title',
                '.title',
                '[class*="title"]'
            ]

            for selector in title_selectors:
                element = soup.select_one(selector)
                if element:
                    title_text = element.get_text().strip()
                    if title_text and len(title_text) > 5:
                        print(f"         ✅ CSS选择器提取: {title_text}")
                        return title_text

            print(f"         ❌ 未找到标题")
            return None

        except Exception as e:
            print(f"      ❌ 提取标题异常: {e}")
            return None

    def extract_field(self, soup: BeautifulSoup, field_names: list) -> Optional[str]:
        """提取指定字段的值"""
        try:
            # 方法1: 表格提取
            for field_name in field_names:
                elements = soup.find_all(string=lambda text: text and field_name in text)
                for element in elements:
                    parent = element.parent
                    if parent and parent.name in ['th', 'td']:
                        next_cell = parent.find_next_sibling(['td', 'th'])
                        if next_cell:
                            value = next_cell.get_text(strip=True)
                            if value and value != '-' and len(value) < 200:
                                print(f"         ✅ 表格提取 {field_name}: {value}")
                                return value

            # 方法2: 定义列表提取
            for field_name in field_names:
                elements = soup.find_all(string=lambda text: text and field_name in text)
                for element in elements:
                    parent = element.parent
                    if parent and parent.name == 'dt':
                        dd = parent.find_next_sibling('dd')
                        if dd:
                            value = dd.get_text(strip=True)
                            if value and value != '-' and len(value) < 200:
                                print(f"         ✅ 定义列表提取 {field_name}: {value}")
                                return value

            # 方法3: 冒号分隔提取
            for field_name in field_names:
                elements = soup.find_all(string=lambda text: text and field_name in text)
                for element in elements:
                    parent = element.parent
                    if parent:
                        parent_text = parent.get_text(strip=True)
                        if ':' in parent_text or '：' in parent_text:
                            parts = re.split(r'[:]|[：]', parent_text, 1)
                            if len(parts) > 1 and field_name in parts[0]:
                                value = parts[1].strip()
                                if value and value != '-' and len(value) < 200:
                                    print(f"         ✅ 冒号分隔提取 {field_name}: {value}")
                                    return value

            return None

        except Exception as e:
            print(f"      ❌ 提取字段异常: {e}")
            return None

    def extract_genre(self, soup: BeautifulSoup) -> Optional[str]:
        """提取ジャンル信息"""
        try:
            # 方法1: 查找商品详情表格中的ジャンル
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        header = cells[0].get_text(strip=True)
                        if 'ジャンル' in header:
                            genre_cell = cells[1]
                            # 查找链接
                            genre_links = genre_cell.find_all('a')
                            if genre_links:
                                genres = []
                                for link in genre_links:
                                    genre_text = link.get_text(strip=True)
                                    if genre_text and len(genre_text) > 1:
                                        genres.append(genre_text)

                                if genres:
                                    return ', '.join(genres[:10])  # 最多10个类型

                            # 如果没有链接，直接获取文本
                            genre_text = genre_cell.get_text(strip=True)
                            if genre_text and genre_text != '-':
                                return genre_text

            # 方法2: 查找ジャンル相关的链接
            genre_links = soup.find_all('a', href=re.compile(r'genre'))
            if genre_links:
                genres = []
                for link in genre_links:
                    genre_text = link.get_text(strip=True)
                    if genre_text and len(genre_text) > 1:
                        genres.append(genre_text)

                if genres:
                    return ', '.join(genres[:10])  # 最多10个类型

            # 方法3: 使用通用提取方法
            genre_text = self.extract_field(soup, ['ジャンル'])
            return genre_text

        except Exception as ex:
            print(f"      ❌ 提取类型异常: {ex}")
            return None

    def extract_rating(self, soup: BeautifulSoup) -> Optional[str]:
        """提取平均評価信息"""
        try:
            # 方法1: 查找"X.XX点"格式
            page_text = soup.get_text()
            rating_match = re.search(r'(\d+\.?\d*)点', page_text)
            if rating_match:
                rating = rating_match.group(1)
                print(f"         ✅ 点数格式提取评分: {rating}")
                return rating

            # 方法2: 查找表格中的平均評価
            elements = soup.find_all(string=lambda text: text and '平均評価' in text)
            for element in elements:
                parent = element.parent
                if parent and parent.name in ['th', 'td']:
                    next_cell = parent.find_next_sibling(['td', 'th'])
                    if next_cell:
                        rating_text = next_cell.get_text(strip=True)
                        if rating_text and rating_text != 'レビューを見る':
                            rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                            if rating_match:
                                rating = rating_match.group(1)
                                print(f"         ✅ 表格提取评分: {rating}")
                                return rating

            # 方法3: 其他评分模式
            rating_patterns = [
                r'平均評価(\d+\.?\d*)点',
                r'評価(\d+\.?\d*)点',
                r'★(\d+\.?\d*)',
                r'(\d+\.?\d*)星'
            ]

            for pattern in rating_patterns:
                match = re.search(pattern, page_text)
                if match:
                    rating = match.group(1)
                    print(f"         ✅ 模式提取评分: {rating}")
                    return rating

            return None

        except Exception as ex:
            print(f"      ❌ 提取评分异常: {ex}")
            return None

    def generate_number_from_cid(self, cid: str) -> Optional[str]:
        """从CID生成番号"""
        try:
            # 移除数字前缀（如果存在）
            cid_clean = re.sub(r'^[0-9]+', '', cid)

            # 查找字母和数字的分界点
            match = re.match(r'^([a-zA-Z]+)([0-9]+)', cid_clean)
            if match:
                prefix = match.group(1).upper()
                number = match.group(2).lstrip('0')  # 移除前导零
                if number:  # 确保数字部分不为空
                    return f"{prefix}-{number}"

            return None

        except Exception as e:
            print(f"      ❌ 生成番号异常: {e}")
            return None
    
    def crawl_single_page(self, page_number: int) -> Tuple[bool, List[Dict], List[str]]:
        """爬取单页数据并获取详情信息，返回(成功状态, 作品数据, 失败的CID列表)"""
        url = f"https://video.dmm.co.jp/av/list/?page={page_number}"

        try:
            print(f"   🔍 爬取第 {page_number} 页")
            print(f"      🌐 访问: {url}")

            # 访问页面
            self.driver.get(url)

            # 等待页面加载（优化：减少等待时间）
            print("      ⏳ 等待页面加载...")
            time.sleep(1)

            # 检查是否是404页面
            current_url = self.driver.current_url
            page_title = self.driver.title

            # 检测404错误页面
            if "404" in page_title or "Not Found" in page_title or "指定されたページが見つかりません" in self.driver.page_source:
                print(f"      ❌ 第{page_number}页不存在 (404错误)")
                print(f"      📄 页面标题: {page_title}")
                print(f"      🔗 当前URL: {current_url}")
                return False, [], []

            # 检查是否被重定向到年龄验证页面
            if "age_check" in current_url:
                print("      🔐 检测到年龄验证页面，正在处理...")

                # 查找并点击"はい"按钮
                try:
                    yes_button = self.driver.find_element(By.LINK_TEXT, "はい")
                    yes_button.click()
                    print("      ✅ 已点击年龄验证按钮")

                    # 等待重定向完成（优化：减少等待时间）
                    time.sleep(3)

                except NoSuchElementException:
                    print("      ❌ 未找到年龄验证按钮")
                    return False, [], []

            # 再次等待页面完全加载（优化：减少等待时间）
            time.sleep(3)

            # 查找包含CID的链接
            cid_links = self.driver.find_elements(By.CSS_SELECTOR, '[href*="cid="]')

            if cid_links:
                print(f"      ✅ 找到 {len(cid_links)} 个CID链接")

                # 提取CID和URL，并获取详情信息
                works_data = []
                seen_cids = set()
                failed_cids = []  # 记录本页失败的CID

                for i, link in enumerate(cid_links, 1):
                    try:
                        href = link.get_attribute('href')
                        if href:
                            cid = self.extract_cid_from_url(href)
                            if cid and cid not in seen_cids:
                                seen_cids.add(cid)

                                print(f"      📄 处理作品 {i}/{len(cid_links)}: {cid}")

                                # 基本信息
                                work_info = {
                                    'cid': cid,
                                    'detail_url': href,
                                    'page_number': page_number,
                                    'crawl_time': datetime.now().isoformat()
                                }

                                # 获取详情信息
                                detail_info = self.extract_detail_info(href, cid)
                                if detail_info:
                                    work_info.update(detail_info)
                                    works_data.append(work_info)
                                else:
                                    # 详情信息获取失败，记录失败的CID
                                    failed_cids.append(cid)
                                    print(f"      ❌ 详情获取失败: {cid}")

                                # 添加延迟避免被反爬（优化：减少延迟）
                                time.sleep(0.3)

                    except Exception as e:
                        print(f"      ❌ 处理作品异常: {e}")
                        continue

                print(f"      ✅ 完成处理 {len(works_data)} 个作品，失败 {len(failed_cids)} 个")
                return True, works_data, failed_cids
            else:
                print("      ❌ 未找到CID链接")

                # 保存页面源码用于调试
                with open(f'debug_firefox_page_{page_number}.html', 'w', encoding='utf-8') as f:
                    f.write(self.driver.page_source)
                print(f"      📄 页面源码已保存到 debug_firefox_page_{page_number}.html")

                return True, [], []

        except Exception as e:
            print(f"      ❌ 爬取异常: {e}")
            return False, [], []
    
    def save_works_to_db(self, works_data: List[Dict]):
        """保存作品数据到数据库"""
        if not works_data:
            return

        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            for work in works_data:
                cursor.execute('''
                    INSERT OR REPLACE INTO dmm_works
                    (cid, title, number, studio, detail_url, page_number, crawl_time,
                     release_date, sale_date, duration, actress, director, series, maker, label,
                     genre, rating, detail_crawled, detail_crawl_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    work.get('cid'),
                    work.get('title'),
                    work.get('number'),
                    work.get('studio'),
                    work.get('detail_url'),
                    work.get('page_number'),
                    work.get('crawl_time'),
                    work.get('release_date'),
                    work.get('sale_date'),
                    work.get('duration'),
                    work.get('actress'),
                    work.get('director'),
                    work.get('series'),
                    work.get('maker'),
                    work.get('label'),
                    work.get('genre'),
                    work.get('rating'),
                    work.get('detail_crawled', 1),
                    work.get('detail_crawl_time')
                ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def check_existing_pages(self, start_page: int, end_page: int):
        """检查数据库中已存在的页面"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            existing_pages = []
            for page_num in range(start_page, end_page + 1):
                cursor.execute("SELECT COUNT(*) FROM dmm_works WHERE page_number = ?", (page_num,))
                count = cursor.fetchone()[0]
                if count > 0:
                    existing_pages.append(page_num)

            conn.close()
            return existing_pages

        except Exception as e:
            print(f"⚠️ 检查已存在页面时出错: {e}")
            return []

    def crawl_multiple_pages(self, start_page: int = 1, end_page: int = 10, skip_existing: bool = True):
        """爬取多页数据"""
        print("🦊 DMM Firefox多页爬虫")
        print("=" * 50)

        print(f"📋 爬取配置:")
        print(f"   页面范围: {start_page} - {end_page}")
        print(f"   预计作品数: {(end_page - start_page + 1) * 120}")
        print(f"   页面间隔: 5秒（高速模式）")
        print(f"   跳过已存在: {'是' if skip_existing else '否'}")

        # 检查已存在的页面
        if skip_existing:
            existing_pages = self.check_existing_pages(start_page, end_page)
            if existing_pages:
                print(f"📊 数据库中已存在的页面: {existing_pages}")
                print(f"💡 将跳过这些页面，避免重复爬取")

        # 初始化浏览器
        if not self.init_driver():
            print("❌ Firefox浏览器初始化失败")
            return

        total_works = 0
        success_pages = 0
        failed_pages = 0
        failed_cids = []  # 记录失败的CID
        failed_pages_list = []  # 记录失败的页面

        try:
            for page_num in range(start_page, end_page + 1):
                print(f"\n📄 处理第 {page_num} 页 ({page_num - start_page + 1}/{end_page - start_page + 1})")

                # 检查是否跳过已存在的页面
                if skip_existing and page_num in existing_pages:
                    print(f"   ⏭️ 跳过第 {page_num} 页（数据库中已存在）")
                    success_pages += 1  # 计入成功页面
                    continue

                # 爬取单页
                success, works_data, page_failed_cids = self.crawl_single_page(page_num)

                if success:
                    # 保存数据
                    self.save_works_to_db(works_data)
                    success_pages += 1
                    total_works += len(works_data)

                    print(f"   ✅ 第 {page_num} 页完成，保存 {len(works_data)} 个作品")

                    # 记录本页失败的CID
                    if page_failed_cids:
                        failed_cids.extend(page_failed_cids)
                        print(f"   ⚠️ 本页失败CID数量: {len(page_failed_cids)}")

                    # 显示进度统计
                    print(f"   📊 累计进度: {success_pages}页成功, {failed_pages}页失败, 总计{total_works}个作品")

                else:
                    failed_pages += 1
                    failed_pages_list.append(page_num)
                    print(f"   ❌ 第 {page_num} 页失败")

                    # 检查是否是404错误，如果是则停止爬取
                    if self.driver and "404" in self.driver.title:
                        print(f"   🛑 检测到404页面，停止爬取（页面{page_num}不存在）")
                        print(f"   💡 建议：网站实际只有{page_num-1}页内容")
                        break

                # 页面间隔（优化：减少页面间等待时间）
                if page_num < end_page:
                    print("   😴 等待5秒后继续下一页...")
                    time.sleep(5)

            # 最终统计
            print(f"\n🎉 爬取完成!")
            print(f"📊 最终统计:")
            print(f"   成功页面: {success_pages}/{end_page - start_page + 1}")
            print(f"   失败页面: {failed_pages}/{end_page - start_page + 1}")
            print(f"   总作品数: {total_works}")
            print(f"   失败CID数量: {len(failed_cids)}")
            print(f"   数据库文件: {self.db_file}")

            # 打印失败信息
            self._print_failed_summary(failed_pages_list, failed_cids)

        finally:
            # 关闭浏览器
            if self.driver:
                self.driver.quit()
                print("🔚 Firefox浏览器已关闭")

    def _print_failed_summary(self, failed_pages_list: List[int], failed_cids: List[str]):
        """打印失败信息汇总"""
        if failed_pages_list or failed_cids:
            print(f"\n❌ 失败信息汇总:")
            print("=" * 50)

            if failed_pages_list:
                print(f"🚫 失败页面: {failed_pages_list}")
                print(f"💡 建议重新爬取这些页面")

            if failed_cids:
                print(f"\n🚫 失败的CID列表 (共{len(failed_cids)}个):")
                print("=" * 30)

                # 按行打印，每行10个CID
                for i in range(0, len(failed_cids), 10):
                    batch = failed_cids[i:i+10]
                    print("   " + ", ".join(batch))

                print(f"\n💡 重试建议:")
                print(f"   1. 检查网络连接是否稳定")
                print(f"   2. 适当增加延迟时间")
                print(f"   3. 手动验证这些CID是否存在")
                print(f"   4. 可以单独重试这些失败的CID")

                # 保存失败CID到文件
                failed_file = f"failed_cids_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                try:
                    with open(failed_file, 'w', encoding='utf-8') as f:
                        f.write("# 失败的CID列表\n")
                        f.write(f"# 生成时间: {datetime.now().isoformat()}\n")
                        f.write(f"# 总数量: {len(failed_cids)}\n\n")
                        for cid in failed_cids:
                            f.write(f"{cid}\n")
                    print(f"📄 失败CID已保存到: {failed_file}")
                except Exception as e:
                    print(f"⚠️ 保存失败CID文件时出错: {e}")
            else:
                print(f"✅ 所有CID都成功获取了详情信息！")

    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("🔚 Firefox浏览器已关闭")

    def crawl_first_page(self):
        """只爬取第一页进行测试"""
        self.crawl_multiple_pages(1, 1)

def get_user_choice():
    """获取用户选择（带重试机制）"""
    while True:
        print("\n📋 爬取选项:")
        print("   1. 测试单页 (第1页) - 推荐先测试")
        print("   2. 爬取前5页 (推荐)")
        print("   3. 爬取前10页")
        print("   4. 自定义页面范围")
        print("   5. 续爬模式 (跳过已存在页面)")
        print("   6. 退出程序")

        choice = input("\n请选择爬取方式 (1-6): ").strip()

        if choice in ['1', '2', '3', '4', '5', '6']:
            return choice
        else:
            print("❌ 无效选择，请输入 1-6 之间的数字")
            print("=" * 30)

def get_page_range():
    """获取页面范围（带重试机制）"""
    while True:
        try:
            print("\n� 请输入页面范围:")
            print("   💡 提示: 可以输入 '6-15' 或分别输入起始和结束页码")

            # 尝试解析范围格式
            range_input = input("页面范围 (如: 6-15 或直接输入起始页码): ").strip()

            if '-' in range_input:
                # 范围格式: 6-15
                parts = range_input.split('-')
                if len(parts) == 2:
                    start_page = int(parts[0].strip())
                    end_page = int(parts[1].strip())
                else:
                    raise ValueError("范围格式错误")
            else:
                # 单独输入格式
                start_page = int(range_input)
                end_page = int(input("结束页码: ").strip())

            # 验证页码范围
            if start_page < 1:
                print("❌ 起始页码必须大于等于1")
                continue
            if end_page < start_page:
                print("❌ 结束页码必须大于等于起始页码")
                continue
            if end_page > 417:
                print("❌ 结束页码不能超过417")
                continue

            return start_page, end_page

        except ValueError as e:
            print(f"❌ 输入格式错误: {e}")
            print("💡 请输入有效的数字或范围格式 (如: 6-15)")
            print("=" * 30)

def main():
    """主函数"""
    print("🦊 DMM Firefox增强爬虫")
    print("=" * 50)

    print("📋 说明:")
    print("   - 使用Firefox浏览器避免Chrome渲染器问题")
    print("   - 直接爬取详情页，获取完整信息（标题、演员、时长等）")
    print("   - 支持单页测试和多页爬取")
    print("   - ⚡ 高速模式：页面间隔5秒，详情页间隔0.3秒，给网站施压")
    print("   - 🔥 大幅优化速度：每页约2分钟（比原来快50%）")

    crawler = DMMFirefoxCrawler()

    while True:
        choice = get_user_choice()

        if choice == '1':
            print("🧪 开始测试第一页...")
            print("⚡ 已大幅优化速度，每页约需2分钟")
            crawler.crawl_first_page()
            break

        elif choice == '2':
            print("🚀 开始爬取前5页...")
            print("⚡ 已大幅优化速度，预计需要约10分钟完成")
            confirm = input("确认继续？(y/N): ").strip().lower()
            if confirm == 'y':
                crawler.crawl_multiple_pages(1, 5)
                break
            else:
                print("爬取已取消")
                continue

        elif choice == '3':
            print("🚀 开始爬取前10页...")
            print("⚡ 已大幅优化速度，预计需要约20分钟完成")
            confirm = input("确认继续？(y/N): ").strip().lower()
            if confirm == 'y':
                crawler.crawl_multiple_pages(1, 10)
                break
            else:
                print("爬取已取消")
                continue

        elif choice == '4':
            start_page, end_page = get_page_range()

            estimated_time = (end_page - start_page + 1) * 2  # 每页约2分钟（大幅优化后）
            print(f"\n🚀 开始爬取第{start_page}-{end_page}页...")
            print(f"⚡ 预计需要约{estimated_time:.1f}分钟（已大幅优化速度）")
            print("🔥 高速模式：给网站施加压力，快速获取详情页信息")

            confirm = input("确认继续？(y/N): ").strip().lower()
            if confirm == 'y':
                crawler.crawl_multiple_pages(start_page, end_page)
                break
            else:
                print("爬取已取消")
                continue

        elif choice == '5':
            print("� 续爬模式（智能跳过已存在页面）")
            print("⚠️  将自动检测并跳过数据库中已有的页面")

            start_page, end_page = get_page_range()

            estimated_time = (end_page - start_page + 1) * 2  # 每页约2分钟（大幅优化后）
            print(f"\n🚀 开始续爬第{start_page}-{end_page}页...")
            print(f"⚡ 预计需要约{estimated_time:.1f}分钟（已大幅优化速度）")
            print(f"🔄 智能模式：自动跳过已存在页面，只爬取新内容")

            confirm = input("确认继续？(y/N): ").strip().lower()
            if confirm == 'y':
                crawler.crawl_multiple_pages(start_page, end_page, skip_existing=True)
                break
            else:
                print("爬取已取消")
                continue

        elif choice == '6':
            print("�👋 程序已退出")
            break

if __name__ == "__main__":
    main()
