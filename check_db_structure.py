#!/usr/bin/env python3
"""
检查数据库结构
"""
import sqlite3
import os

def check_database_structure():
    db_file = "dmm_firefox_database.db"
    
    if not os.path.exists(db_file):
        print(f"❌ 数据库文件不存在: {db_file}")
        return
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='dmm_works'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ dmm_works表不存在")
            return
        
        # 获取表结构
        cursor.execute("PRAGMA table_info(dmm_works)")
        columns = cursor.fetchall()
        
        print("📊 现有数据库字段:")
        for i, (cid, name, data_type, not_null, default_value, pk) in enumerate(columns, 1):
            print(f"   {i:2d}. {name} ({data_type})")
        
        # 检查数据量
        cursor.execute("SELECT COUNT(*) FROM dmm_works")
        count = cursor.fetchone()[0]
        print(f"\n📈 数据量: {count} 条记录")
        
        # 检查是否有thumbnail_url字段
        column_names = [col[1] for col in columns]
        if 'thumbnail_url' in column_names:
            print("✅ thumbnail_url字段存在")
        else:
            print("❌ thumbnail_url字段不存在")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")

if __name__ == "__main__":
    check_database_structure()
