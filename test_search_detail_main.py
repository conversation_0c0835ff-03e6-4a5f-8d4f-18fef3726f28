#!/usr/bin/env python3
"""
搜索详情页主要功能测试
测试完整的搜索流程：搜索 -> 获取详情 -> 下载海报 -> 保存数据
"""

import sys
import os
import time
sys.path.append('modules')

def test_basic_search():
    """测试基础搜索功能"""
    print("🔍 测试基础搜索功能")
    print("=" * 50)
    
    try:
        from modules.search_detail import SearchDetailModule
        
        search_detail = SearchDetailModule()
        
        # 测试多个不同类型的番号
        test_cases = [
            ("CAWD-797", "单一映射，数据库有详情"),
            ("MILK-251", "多重映射，已优化"),
            ("PRED-123", "单一映射，需要实时爬取"),
            ("IPX-123", "常见厂商测试")
        ]
        
        results = {}
        
        for code, description in test_cases:
            print(f"\n🧪 测试: {code} ({description})")
            
            start_time = time.time()
            result = search_detail.search_dmm_enhanced(code)
            duration = time.time() - start_time
            
            results[code] = result
            
            if result.get('success'):
                print(f"   ✅ 搜索成功 - 耗时: {duration:.2f}秒")
                print(f"   📍 CID: {result.get('cid', 'N/A')}")
                print(f"   📝 标题: {result.get('title', 'N/A')[:50]}...")
                print(f"   🎬 演员: {result.get('actress', 'N/A')}")
                print(f"   ⏱️ 时长: {result.get('duration', 'N/A')}")
                print(f"   📊 来源: {result.get('source', 'N/A')}")
            else:
                print(f"   ❌ 搜索失败 - 耗时: {duration:.2f}秒")
                print(f"   💬 原因: {result.get('message', 'N/A')}")
        
        # 统计成功率
        success_count = sum(1 for r in results.values() if r.get('success'))
        success_rate = success_count / len(test_cases)
        
        print(f"\n📊 基础搜索统计:")
        print(f"   成功率: {success_count}/{len(test_cases)} ({success_rate:.1%})")
        
        return results, success_rate >= 0.75
        
    except Exception as e:
        print(f"❌ 基础搜索测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {}, False

def test_poster_download(search_results):
    """测试海报下载功能"""
    print("\n\n🖼️ 测试海报下载功能")
    print("=" * 50)
    
    try:
        from modules.search_detail import SearchDetailModule
        
        search_detail = SearchDetailModule()
        
        # 选择有CID的结果进行海报测试
        test_cids = []
        for code, result in search_results.items():
            if result.get('success') and result.get('cid') and result.get('cid') != 'N/A':
                test_cids.append((code, result.get('cid')))
        
        if not test_cids:
            print("⚠️ 没有有效的CID进行海报测试")
            return False
        
        poster_results = {}
        
        for code, cid in test_cids[:3]:  # 测试前3个
            print(f"\n🧪 测试海报下载: {code} (CID: {cid})")
            
            start_time = time.time()
            poster_result = search_detail.get_poster_with_cid(cid)
            duration = time.time() - start_time
            
            poster_results[code] = poster_result
            
            if poster_result.get('success'):
                print(f"   ✅ 海报下载成功 - 耗时: {duration:.2f}秒")
                
                poster_size = len(poster_result.get('poster_bytes', b''))
                thumb_size = len(poster_result.get('thumb_bytes', b''))
                
                print(f"   🖼️ 高清海报: {poster_size} bytes")
                print(f"   🖼️ 缩略图: {thumb_size} bytes")
                print(f"   🌐 海报URL: {poster_result.get('poster_url', 'N/A')}")
                
                # 可选：保存海报到文件
                if poster_result.get('poster_bytes'):
                    poster_file = f"test_poster_{code}.jpg"
                    try:
                        with open(poster_file, 'wb') as f:
                            f.write(poster_result['poster_bytes'])
                        print(f"   💾 海报已保存: {poster_file}")
                    except Exception as e:
                        print(f"   ⚠️ 海报保存失败: {e}")
                        
            else:
                print(f"   ❌ 海报下载失败 - 耗时: {duration:.2f}秒")
                print(f"   💬 原因: {poster_result.get('message', 'N/A')}")
        
        # 统计海报下载成功率
        poster_success_count = sum(1 for r in poster_results.values() if r.get('success'))
        poster_success_rate = poster_success_count / len(poster_results) if poster_results else 0
        
        print(f"\n📊 海报下载统计:")
        print(f"   成功率: {poster_success_count}/{len(poster_results)} ({poster_success_rate:.1%})")
        
        return poster_success_rate >= 0.5
        
    except Exception as e:
        print(f"❌ 海报下载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_saving(search_results):
    """测试数据保存功能"""
    print("\n\n💾 测试数据保存功能")
    print("=" * 50)
    
    try:
        from modules.search_detail import SearchDetailModule
        
        search_detail = SearchDetailModule()
        
        # 选择成功的搜索结果进行数据保存测试
        save_results = {}
        
        for code, result in search_results.items():
            if result.get('success') and result.get('cid') and result.get('cid') != 'N/A':
                print(f"\n🧪 测试数据保存: {code}")
                
                try:
                    save_result = search_detail.save_dmm_data_to_json(result)
                    save_results[code] = save_result
                    
                    if save_result.get('success'):
                        print(f"   ✅ 数据保存成功")
                        print(f"   💬 消息: {save_result.get('message', 'N/A')}")
                    else:
                        print(f"   ❌ 数据保存失败")
                        print(f"   💬 原因: {save_result.get('message', 'N/A')}")
                        
                except Exception as e:
                    print(f"   ❌ 数据保存异常: {e}")
                    save_results[code] = {"success": False, "message": str(e)}
        
        # 统计数据保存成功率
        save_success_count = sum(1 for r in save_results.values() if r.get('success'))
        save_success_rate = save_success_count / len(save_results) if save_results else 0
        
        print(f"\n📊 数据保存统计:")
        print(f"   成功率: {save_success_count}/{len(save_results)} ({save_success_rate:.1%})")
        
        # 检查生成的配置文件
        config_files = [
            "h_prefix_numbers.json",
            "prefix_h_prefix_map.json", 
            "number_prefixes.json"
        ]
        
        print(f"\n📁 检查配置文件:")
        for config_file in config_files:
            if os.path.exists(config_file):
                file_size = os.path.getsize(config_file)
                print(f"   ✅ {config_file} ({file_size} bytes)")
            else:
                print(f"   ⚠️ {config_file} (不存在)")
        
        return save_success_rate >= 0.5
        
    except Exception as e:
        print(f"❌ 数据保存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_url_parsing():
    """测试手动URL解析功能"""
    print("\n\n🔗 测试手动URL解析功能")
    print("=" * 50)
    
    try:
        from modules.search_detail import SearchDetailModule
        
        search_detail = SearchDetailModule()
        
        # 测试DMM详情页URL解析
        test_urls = [
            "https://www.dmm.co.jp/digital/videoa/-/detail/=/cid=cawd00797/",
            "https://www.dmm.co.jp/digital/videoa/-/detail/=/cid=h_1240milk00251/",
        ]
        
        parse_results = {}
        
        for url in test_urls:
            print(f"\n🧪 测试URL解析: {url}")
            
            try:
                parse_result = search_detail.parse_manual_dmm_url(url)
                parse_results[url] = parse_result
                
                if parse_result.get('success'):
                    print(f"   ✅ URL解析成功")
                    print(f"   📍 CID: {parse_result.get('cid', 'N/A')}")
                    print(f"   🏭 厂牌: {parse_result.get('label', 'N/A')}")
                    print(f"   💬 消息: {parse_result.get('message', 'N/A')}")
                else:
                    print(f"   ❌ URL解析失败")
                    print(f"   💬 原因: {parse_result.get('message', 'N/A')}")
                    
            except Exception as e:
                print(f"   ❌ URL解析异常: {e}")
                parse_results[url] = {"success": False, "message": str(e)}
        
        # 统计URL解析成功率
        parse_success_count = sum(1 for r in parse_results.values() if r.get('success'))
        parse_success_rate = parse_success_count / len(parse_results) if parse_results else 0
        
        print(f"\n📊 URL解析统计:")
        print(f"   成功率: {parse_success_count}/{len(parse_results)} ({parse_success_rate:.1%})")
        
        return parse_success_rate >= 0.5
        
    except Exception as e:
        print(f"❌ URL解析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_benchmark():
    """性能基准测试"""
    print("\n\n⚡ 性能基准测试")
    print("=" * 50)
    
    try:
        from modules.search_detail import SearchDetailModule
        
        search_detail = SearchDetailModule()
        
        # 连续搜索测试
        test_codes = ["CAWD-797", "MILK-251", "PRED-123"]
        total_time = 0
        success_count = 0
        
        print("🔍 连续搜索性能测试:")
        
        for i, code in enumerate(test_codes, 1):
            print(f"   {i}/3: {code}")
            
            start_time = time.time()
            result = search_detail.search_dmm_enhanced(code)
            duration = time.time() - start_time
            
            total_time += duration
            if result.get('success'):
                success_count += 1
            
            print(f"      耗时: {duration:.2f}秒 - {'成功' if result.get('success') else '失败'}")
        
        avg_time = total_time / len(test_codes)
        success_rate = success_count / len(test_codes)
        
        print(f"\n📊 性能基准:")
        print(f"   平均搜索时间: {avg_time:.2f}秒")
        print(f"   总耗时: {total_time:.2f}秒")
        print(f"   成功率: {success_rate:.1%}")
        
        # 性能评级
        if avg_time < 2:
            performance_grade = "🏆 优秀"
        elif avg_time < 5:
            performance_grade = "✅ 良好"
        elif avg_time < 10:
            performance_grade = "⚠️ 一般"
        else:
            performance_grade = "❌ 需要优化"
        
        print(f"   性能评级: {performance_grade}")
        
        return avg_time < 10 and success_rate >= 0.75
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 搜索详情页主要功能完整测试")
    print("=" * 60)
    
    print("📋 测试项目:")
    print("   1. 基础搜索功能")
    print("   2. 海报下载功能") 
    print("   3. 数据保存功能")
    print("   4. 手动URL解析")
    print("   5. 性能基准测试")
    
    print("\n" + "=" * 60)
    
    test_results = {}
    
    try:
        # 1. 基础搜索测试
        search_results, search_success = test_basic_search()
        test_results['基础搜索'] = search_success
        
        # 2. 海报下载测试
        poster_success = test_poster_download(search_results)
        test_results['海报下载'] = poster_success
        
        # 3. 数据保存测试
        save_success = test_data_saving(search_results)
        test_results['数据保存'] = save_success
        
        # 4. URL解析测试
        parse_success = test_manual_url_parsing()
        test_results['URL解析'] = parse_success
        
        # 5. 性能测试
        performance_success = test_performance_benchmark()
        test_results['性能基准'] = performance_success
        
        # 最终评估
        print("\n" + "=" * 60)
        print("📊 测试结果总结:")
        
        for test_name, success in test_results.items():
            status = "✅ 通过" if success else "❌ 失败"
            print(f"   {test_name}: {status}")
        
        success_count = sum(test_results.values())
        total_tests = len(test_results)
        overall_success_rate = success_count / total_tests
        
        print(f"\n🎯 总体成功率: {success_count}/{total_tests} ({overall_success_rate:.1%})")
        
        if overall_success_rate >= 0.8:
            print("🎉 搜索详情页功能优秀！所有主要功能正常工作")
        elif overall_success_rate >= 0.6:
            print("👍 搜索详情页功能良好！大部分功能正常工作")
        else:
            print("⚠️ 搜索详情页功能需要改进")
        
        print("\n💡 使用建议:")
        if test_results.get('基础搜索', False):
            print("   ✅ 可以正常使用搜索功能")
        if test_results.get('海报下载', False):
            print("   ✅ 可以下载和保存海报图片")
        if test_results.get('数据保存', False):
            print("   ✅ 可以保存搜索数据到配置文件")
        if test_results.get('性能基准', False):
            print("   ✅ 系统性能表现良好")
            
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
