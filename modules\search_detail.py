#!/usr/bin/env python3
"""
搜索详情页功能模块
提供DMM搜索、海报获取、数据保存、文件重命名等完整功能
"""

import os
import re
import json
import asyncio
import requests
from datetime import datetime
from PIL import Image
import io
import traceback
from typing import Optional, Dict, List

# 导入FastDMM搜索引擎
try:
    from .fast_dmm_search import FastDMMSearch
    FAST_SEARCH_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ FastDMM搜索引擎不可用: {e}")
    FAST_SEARCH_AVAILABLE = False

try:
    from .auto_mapping_learner import AutoMappingLearner
    AUTO_LEARNING_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 自动映射学习器不可用: {e}")
    AUTO_LEARNING_AVAILABLE = False


class SearchDetailModule:
    """搜索详情页功能模块"""

    def __init__(self, enable_fast_search: bool = True, enable_auto_learning: bool = True):
        """初始化模块"""
        self.dmm_tools = None
        self.fast_search = None
        self.auto_learner = None
        self.enable_fast_search = enable_fast_search and FAST_SEARCH_AVAILABLE
        self.enable_auto_learning = enable_auto_learning and AUTO_LEARNING_AVAILABLE

        self._init_dmm_tools()
        self._init_fast_search()
        self._init_auto_learner()
    
    def _init_dmm_tools(self):
        """初始化dmm_tools模块"""
        try:
            import dmm_tools
            self.dmm_tools = dmm_tools
        except ImportError as e:
            print(f"警告: 无法导入dmm_tools模块: {e}")
            self.dmm_tools = None

    def _init_fast_search(self):
        """初始化FastDMM搜索引擎"""
        if self.enable_fast_search and FAST_SEARCH_AVAILABLE:
            try:
                # 尝试从全局缓存获取FastDMM实例
                import streamlit as st
                if hasattr(st, 'session_state') and "fast_dmm_search_engine" in st.session_state:
                    self.fast_search = st.session_state.fast_dmm_search_engine
                    print("♻️ 从session_state获取已缓存的FastDMM搜索引擎")
                else:
                    print("🔧 首次初始化FastDMM搜索引擎...")
                    self.fast_search = FastDMMSearch()
                    # 缓存到session_state
                    if hasattr(st, 'session_state'):
                        st.session_state.fast_dmm_search_engine = self.fast_search
                    print("✅ FastDMM搜索引擎初始化成功并已缓存")
            except Exception as e:
                print(f"❌ FastDMM搜索引擎初始化失败: {e}")
                self.fast_search = None
                self.enable_fast_search = False
        else:
            print("⚠️ FastDMM搜索引擎已禁用")

    def _init_auto_learner(self):
        """初始化自动映射学习器"""
        if self.enable_auto_learning and AUTO_LEARNING_AVAILABLE:
            try:
                # 尝试从session_state获取自动学习器实例
                import streamlit as st
                if hasattr(st, 'session_state') and "auto_mapping_learner" in st.session_state:
                    self.auto_learner = st.session_state.auto_mapping_learner
                    print("♻️ 从session_state获取已缓存的自动映射学习器")
                else:
                    print("🔧 首次初始化自动映射学习器...")
                    self.auto_learner = AutoMappingLearner()
                    # 缓存到session_state
                    if hasattr(st, 'session_state'):
                        st.session_state.auto_mapping_learner = self.auto_learner
                    print("✅ 自动映射学习器初始化成功并已缓存")
            except Exception as e:
                print(f"❌ 自动映射学习器初始化失败: {e}")
                self.auto_learner = None
                self.enable_auto_learning = False
        else:
            print("⚠️ 自动映射学习器已禁用")
    
    def search_dmm_enhanced(self, code, force_crawl=False):
        """
        增强的DMM搜索功能 - 现在支持智能多映射检测和强制爬取
        返回详细的搜索结果，包括基本信息和状态

        Args:
            code: 番号
            force_crawl: 是否强制爬取（跳过本地搜索）
        """
        print(f"🔍 开始智能搜索番号: {code}")
        if force_crawl:
            print("🕷️ 强制爬取模式")

        try:
            # 如果强制爬取，直接使用爬虫
            if force_crawl:
                print(f"🕷️ 强制爬取模式，直接使用DMM Firefox爬虫")
                return self._force_crawl_search(code)

            # 1. 优先使用FastDMM智能搜索引擎
            if self.enable_fast_search and self.fast_search:
                print(f"🧠 使用智能映射检测搜索引擎")
                search_response = self.fast_search.intelligent_search(code)

                if search_response['success']:
                    if search_response['is_multiple']:
                        # 多重映射结果，简单优化：返回第一个结果而不是N/A
                        results = search_response['results']
                        if results:
                            print(f"🔀 多重映射简单优化: 选择第一个结果")
                            return self._format_single_result(results[0], code)
                        else:
                            return self._format_multiple_results(search_response, code)
                    else:
                        # 单一结果，尝试补充详细信息
                        if search_response['results']:
                            fast_result = self._format_single_result(search_response['results'][0], code)

                            # 尝试从DMM Firefox数据库或实时爬取获取详细信息
                            enhanced_result = self._enhance_with_crawler_info(fast_result, code)
                            return enhanced_result
                        else:
                            return self._create_error_result(code, "搜索结果为空")
                else:
                    # 智能搜索失败，尝试备用搜索
                    return self._try_fallback_search(code, search_response['message'])

            # 如果FastDMM不可用，使用备用搜索
            return self._fallback_dmm_search(code)

        except Exception as e:
            error_msg = f"搜索过程出错: {str(e)}"
            print(f"❌ {error_msg}")
            traceback.print_exc()
            return {
                "success": False,
                "code": code,
                "cid": "",
                "label": "",
                "url": "",
                "message": f"❌ {error_msg}"
            }

    def manual_search_with_crawl(self, code):
        """
        手动搜索（强制实时爬取）

        Args:
            code: 番号

        Returns:
            搜索结果字典（包含完整详情）
        """
        print(f"🔍 手动搜索（强制爬取）: {code}")
        return self.search_dmm_enhanced(code, force_crawl=True)

    def _force_crawl_search(self, code):
        """强制爬取搜索"""
        try:
            print(f"🕷️ 强制爬取模式: {code}")
            from dmm_firefox_crawler import DMMFirefoxCrawler

            # 创建爬虫实例
            crawler = DMMFirefoxCrawler()

            # 直接进行实时爬取
            detail_info = self._realtime_crawl_details(code, crawler)
            if detail_info:
                print(f"✅ 强制爬取成功: {code}")
                return self._format_firefox_result(detail_info, code)
            else:
                return {
                    "success": False,
                    "code": code,
                    "cid": "",
                    "label": "",
                    "url": "",
                    "message": f"❌ 强制爬取失败: {code}"
                }

        except Exception as e:
            print(f"❌ 强制爬取异常: {e}")
            return {
                "success": False,
                "code": code,
                "cid": "",
                "label": "",
                "url": "",
                "message": f"❌ 强制爬取异常: {str(e)}"
            }

    def _fallback_dmm_search(self, code):
        """🔧 增强的DMM搜索（集成DMM Firefox详情爬虫）"""

        # 🎯 优先尝试DMM Firefox详情爬虫
        try:
            print(f"🕷️ 尝试DMM Firefox详情爬虫")
            from dmm_firefox_crawler import DMMFirefoxCrawler

            # 创建爬虫实例
            crawler = DMMFirefoxCrawler()

            # 尝试从数据库查找
            detail_info = self._search_firefox_database(code, crawler)
            if detail_info:
                print(f"✅ 在DMM Firefox数据库中找到: {code}")
                return self._format_firefox_result(detail_info, code)

            # 数据库中没有，尝试实时爬取
            print(f"🔍 数据库中未找到，尝试实时爬取: {code}")
            detail_info = self._realtime_crawl_details(code, crawler)
            if detail_info:
                print(f"✅ 实时爬取成功: {code}")
                return self._format_firefox_result(detail_info, code)

        except Exception as e:
            print(f"⚠️ DMM Firefox爬虫失败: {e}")

        # 🎯 备用：尝试DMMSearchCrawler
        try:
            print(f"🔄 回退到DMMSearchCrawler")
            from modules.dmm_search_crawler import DMMSearchCrawler

            # 解析番号
            studio, number = self._parse_code_for_crawler(code)
            if studio and number:
                crawler = DMMSearchCrawler()
                mappings = crawler._crawl_single_number(studio, number)

                if mappings:
                    # 使用第一个映射结果
                    mapping = mappings[0]
                    result = {
                        "success": True,
                        "code": code,
                        "cid": mapping["cid"],
                        "label": f"{studio} Series",
                        "url": mapping["url"],
                        "message": f"✅ 备用爬虫搜索成功！番号: {code}, CID: {mapping['cid']}"
                    }

                    print(f"🎉 备用爬虫搜索成功: {code} -> {mapping['cid']}")
                    return result
                else:
                    print(f"⚠️ 备用爬虫未找到映射: {code}")
            else:
                print(f"⚠️ 无法解析番号格式: {code}")

        except Exception as e:
            print(f"⚠️ 备用爬虫失败: {e}")

        # 🔄 如果所有爬虫都失败，回退到传统方法
        if not self.dmm_tools:
            return {
                "success": False,
                "code": code,
                "cid": "",
                "label": "",
                "url": "",
                "message": "❌ 所有搜索方法都失败了"
            }

    def _search_firefox_database(self, code: str, crawler) -> Optional[Dict]:
        """在DMM Firefox数据库中搜索"""
        try:
            import sqlite3
            import os

            if not os.path.exists(crawler.db_file):
                return None

            conn = sqlite3.connect(crawler.db_file)
            cursor = conn.cursor()

            # 标准化查询
            normalized_code = code.upper().replace(" ", "").replace("-", "")

            # 多种搜索策略
            search_queries = [
                ("SELECT * FROM dmm_works WHERE cid = ? LIMIT 1", (code.lower(),)),
                ("SELECT * FROM dmm_works WHERE number = ? LIMIT 1", (code.upper(),)),
                ("SELECT * FROM dmm_works WHERE UPPER(REPLACE(REPLACE(number, '-', ''), ' ', '')) = ? LIMIT 1", (normalized_code,)),
            ]

            for sql, params in search_queries:
                cursor.execute(sql, params)
                row = cursor.fetchone()
                if row:
                    conn.close()
                    return self._row_to_dict(row)

            conn.close()
            return None

        except Exception as e:
            print(f"❌ Firefox数据库搜索失败: {e}")
            return None

    def _realtime_crawl_details(self, code: str, crawler) -> Optional[Dict]:
        """实时爬取详情信息"""
        try:
            # 生成可能的CID列表
            possible_cids = self._generate_possible_cids(code)

            for cid in possible_cids[:3]:  # 限制尝试数量
                print(f"🕷️ 尝试爬取CID: {cid}")

                # 构建详情页URL
                detail_url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={cid}/"

                # 使用爬虫提取详情
                detail_info = crawler.extract_detail_info(detail_url, cid)

                if detail_info:
                    print(f"✅ 实时爬取成功: {cid}")
                    # 确保CID字段正确设置
                    if not detail_info.get("cid"):
                        detail_info["cid"] = cid
                    return detail_info

                # 爬取间隔
                import time
                time.sleep(1)

            return None

        except Exception as e:
            print(f"❌ 实时爬取失败: {e}")
            return None

    def _generate_possible_cids(self, code: str) -> List[str]:
        """生成可能的CID列表（支持多重映射，仅使用5位数字格式）"""
        cids = []

        # 如果已经是CID格式
        if re.match(r'^[a-z]+\d+$', code.lower()):
            match = re.match(r'^([a-z]+)(\d+)$', code.lower())
            if match:
                prefix, number = match.groups()
                # 只使用5位数字格式
                formatted_cid = f"{prefix}{int(number):05d}"
                cids.append(formatted_cid)

        # 如果是番号格式，转换为CID（支持多重映射）
        if re.match(r'^[A-Z]+-\d+$', code.upper()):
            match = re.match(r'^([A-Z]+)-(\d+)$', code.upper())
            if match:
                studio, number = match.groups()

                # 尝试使用FastDMM的映射管理器获取所有可能的映射
                try:
                    # 检查是否有FastDMM搜索引擎和映射管理器
                    fast_search = getattr(self, 'fast_search', None)
                    if fast_search and hasattr(fast_search, 'mapping_manager') and fast_search.mapping_manager:
                        mapping_info = fast_search.mapping_manager.get_mapping_info(studio)

                        if mapping_info['has_mapping']:
                            # 获取所有映射（主映射 + 备选映射）
                            all_mappings = [mapping_info['primary']] + mapping_info.get('alternatives', [])

                            # 为每个映射生成CID
                            for mapping_prefix in all_mappings:
                                formatted_cid = f"{mapping_prefix}{int(number):05d}"
                                if formatted_cid not in cids:  # 避免重复
                                    cids.append(formatted_cid)

                            print(f"🔀 多重映射生成: {studio} -> {len(cids)} 个CID: {cids}")
                            return cids
                        else:
                            print(f"⚠️ 未找到 {studio} 的映射信息")
                    else:
                        print(f"⚠️ FastDMM映射管理器不可用")

                except Exception as e:
                    print(f"⚠️ 获取映射信息时出错: {e}")

                # 如果映射管理器不可用，使用简单的转换
                prefix_lower = studio.lower()
                formatted_cid = f"{prefix_lower}{int(number):05d}"
                cids.append(formatted_cid)

        return cids

    def _row_to_dict(self, row) -> Dict:
        """将数据库行转换为字典"""
        if not row or len(row) < 20:
            return {}

        return {
            "id": row[0],
            "cid": row[1],
            "title": row[2],
            "number": row[3],
            "studio": row[4],
            "detail_url": row[5],
            "page_number": row[6],
            "crawl_time": row[7],
            "release_date": row[8],
            "sale_date": row[9],
            "duration": row[10],
            "actress": row[11],
            "director": row[12],
            "series": row[13],
            "maker": row[14],
            "label": row[15],
            "genre": row[16],
            "rating": row[17],
            "detail_crawled": row[18],
            "detail_crawl_time": row[19]
        }

    def _enhance_with_crawler_info(self, fast_result: Dict, code: str) -> Dict:
        """使用爬虫信息增强FastDMM搜索结果"""
        try:
            print(f"🔍 尝试获取详细信息: {code}")
            from dmm_firefox_crawler import DMMFirefoxCrawler

            # 创建爬虫实例
            crawler = DMMFirefoxCrawler()

            # 先检查数据库
            detail_info = self._search_firefox_database(code, crawler)
            if detail_info:
                print(f"✅ 在DMM Firefox数据库中找到详细信息")
                # 合并FastDMM结果和详细信息
                enhanced_result = fast_result.copy()
                enhanced_result.update({
                    "title": detail_info.get("title", ""),
                    "actress": detail_info.get("actress", ""),
                    "duration": detail_info.get("duration", ""),
                    "rating": detail_info.get("rating", ""),
                    "release_date": detail_info.get("release_date", ""),
                    "genre": detail_info.get("genre", ""),
                    "director": detail_info.get("director", ""),
                    "series": detail_info.get("series", ""),
                    "maker": detail_info.get("maker", ""),
                    "source": "fast_dmm+firefox_database",
                    "message": f"✅ 智能搜索+数据库详情！番号: {code}, CID: {fast_result.get('cid', '')}"
                })
                return enhanced_result
            else:
                print(f"⚠️ 数据库中未找到详细信息，返回FastDMM结果")
                return fast_result

        except Exception as e:
            print(f"⚠️ 详细信息获取失败: {e}")
            return fast_result

    def _format_firefox_result(self, detail_info: Dict, code: str) -> Dict:
        """格式化Firefox爬虫结果"""
        # 确保CID字段正确获取
        cid = detail_info.get("cid", "")
        if not cid:
            # 如果detail_info中没有cid，尝试从其他字段获取
            cid = detail_info.get("id", "")

        return {
            "success": True,
            "code": code,
            "cid": cid,
            "label": detail_info.get("studio", ""),
            "url": detail_info.get("detail_url", ""),
            "title": detail_info.get("title", ""),
            "actress": detail_info.get("actress", ""),
            "duration": detail_info.get("duration", ""),
            "rating": detail_info.get("rating", ""),
            "release_date": detail_info.get("release_date", ""),
            "genre": detail_info.get("genre", ""),
            "director": detail_info.get("director", ""),
            "series": detail_info.get("series", ""),
            "maker": detail_info.get("maker", ""),
            "source": "dmm_firefox_crawler",
            "message": f"✅ DMM Firefox爬虫获取完整详情！番号: {code}, CID: {cid}"
        }

    def _parse_code_for_crawler(self, code: str):
        """🔧 新增：解析番号为厂商和数字，供新爬虫使用"""
        try:
            # 标准化输入
            code_normalized = code.upper().replace("-", "").replace("_", "").strip()

            # 常见的番号格式
            patterns = [
                r'^([A-Z]+)(\d+)$',           # ID021, SSIS001
                r'^([A-Z]+)-(\d+)$',          # ID-021, SSIS-001
                r'^([A-Z]+)_(\d+)$',          # ID_021, SSIS_001
                r'^([A-Z]+)\s+(\d+)$',        # ID 021, SSIS 001
            ]

            for pattern in patterns:
                match = re.match(pattern, code)
                if match:
                    studio = match.group(1).upper()
                    number = int(match.group(2))

                    print(f"   📝 解析番号: {code} -> 厂商: {studio}, 数字: {number}")
                    return studio, number

            print(f"   ❌ 无法解析番号格式: {code}")
            return None, None

        except Exception as e:
            print(f"   ❌ 番号解析异常: {e}")
            return None, None

        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            print(f"🔍 调用dmm_tools.search_dmm_detail({code})")
            result = loop.run_until_complete(self.dmm_tools.search_dmm_detail(code))
            print(f"🔍 dmm_tools.search_dmm_detail返回结果: {result}")

            if result:
                # 验证结果是否与输入番号匹配
                result_cid = result.get("cid", "")
                input_normalized = self._normalize_code_for_comparison(code)
                result_normalized = self._normalize_code_for_comparison(result_cid)

                print(f"🔍 结果验证: 输入={input_normalized}, 结果={result_normalized}")

                # 检查是否匹配（允许一定的变体）
                if self._is_code_match(input_normalized, result_normalized, code, result_cid):
                    success_result = {
                        "success": True,
                        "code": code,
                        "cid": result.get("cid", ""),
                        "label": result.get("label", ""),
                        "url": result.get("url", ""),
                        "message": f"✅ 搜索成功！番号: {code}, CID: {result.get('cid', '')} (传统方式)"
                    }

                    # 🎓 自动学习映射（如果启用）
                    if self.enable_auto_learning and self.auto_learner:
                        try:
                            print(f"🎓 尝试自动学习映射: {code} -> {result_cid}")
                            learning_result = self.auto_learner.learn_from_successful_search(
                                code=code,
                                found_cid=result_cid,
                                search_source="dmm_tools_fallback"
                            )

                            if learning_result["success"]:
                                print(f"✅ 自动学习成功: {learning_result['message']}")
                                success_result["learning_message"] = f"🎓 {learning_result['message']}"

                                # 如果学习成功，尝试重新加载映射管理器
                                if hasattr(self.fast_search, 'mapping_manager'):
                                    self.fast_search.mapping_manager._load_to_memory_cache()
                                    print("♻️ 映射管理器缓存已刷新")
                            else:
                                print(f"⚠️ 自动学习跳过: {learning_result['message']}")
                                success_result["learning_message"] = f"💡 {learning_result['message']}"

                        except Exception as e:
                            print(f"❌ 自动学习异常: {e}")
                            success_result["learning_message"] = f"❌ 自动学习失败: {e}"

                    # 调用数据保存逻辑，更新JSON配置文件
                    try:
                        save_result = self._save_dmm_data_to_json(success_result)
                        print(f"JSON保存结果: {save_result}")
                        if save_result["success"]:
                            success_result["save_message"] = save_result["message"]
                        else:
                            success_result["save_message"] = f"❌ 保存失败: {save_result['message']}"
                    except Exception as e:
                        error_msg = f"JSON保存异常: {str(e)}"
                        print(error_msg)
                        success_result["save_message"] = f"❌ {error_msg}"

                    return success_result
                else:
                    print(f"⚠️ 结果不匹配: 输入 {code} 但找到 {result_cid}")
                    return {
                        "success": False,
                        "code": code,
                        "cid": "",
                        "label": "",
                        "url": "",
                        "message": f"❌ 搜索结果不匹配：输入 {code} 但找到 {result_cid}，请检查番号是否正确"
                    }
            else:
                return {
                    "success": False,
                    "code": code,
                    "cid": "",
                    "label": "",
                    "url": "",
                    "message": f"❌ 未找到番号 {code} 的详情页"
                }
        except Exception as e:
            error_msg = f"传统搜索过程出错: {str(e)}"
            print(f"❌ {error_msg}")
            traceback.print_exc()
            return {
                "success": False,
                "code": code,
                "cid": "",
                "label": "",
                "url": "",
                "message": f"❌ {error_msg}"
            }

    def _normalize_code_for_comparison(self, code):
        """标准化番号用于比较"""
        if not code:
            return ""
        # 移除空格、连字符，转换为小写
        normalized = code.replace(" ", "").replace("-", "").lower()
        return normalized

    def _standardize_cid_format(self, cid):
        """标准化CID格式，确保数字部分有正确的零填充"""
        if not cid:
            return cid

        cid_lower = cid.lower().strip()

        # 匹配标准格式：字母+数字 (如 milk251, ipx123)
        match = re.match(r"^([a-z]+)(\d+)$", cid_lower)
        if match:
            letter_part = match.group(1)
            number_part = match.group(2)

            # 根据不同厂牌的规则进行零填充
            if letter_part in ['milk', 'ipx', 'ipz', 'ssis', 'stars', 'pred']:
                # 这些厂牌通常使用5位数字格式 (如 milk00251)
                if len(number_part) < 5:
                    number_part = number_part.zfill(5)
            elif letter_part in ['fc2', 'fc']:
                # FC2通常使用7位数字
                if len(number_part) < 7:
                    number_part = number_part.zfill(7)
            else:
                # 其他厂牌默认使用5位数字
                if len(number_part) < 5:
                    number_part = number_part.zfill(5)

            return f"{letter_part}{number_part}"

        # 如果不匹配标准格式，返回原始CID
        return cid

    def _generate_poster_cid_list(self, cid):
        """生成可能的海报CID格式列表，与dmm_tools的逻辑保持一致"""
        if not cid:
            return [cid]

        cid_lower = cid.lower().strip()
        possible_cids = [cid_lower]  # 首先尝试原始CID

        # 解析CID格式
        match = re.match(r"^([a-z]+)(\d+)$", cid_lower)
        if match:
            prefix = match.group(1)
            num = int(match.group(2))
            num5 = f"{num:05d}"

            # 添加5位数字格式
            if f"{prefix}{num5}" not in possible_cids:
                possible_cids.append(f"{prefix}{num5}")

            # 尝试从配置文件中获取h_前缀映射
            try:
                # 读取 prefix_h_prefix_map.json
                prefix_h_map = {}
                try:
                    with open("prefix_h_prefix_map.json", "r", encoding="utf-8") as f:
                        prefix_h_map = json.load(f)
                except Exception:
                    pass

                h_prefixes = prefix_h_map.get(prefix, [])
                if h_prefixes:
                    for n in h_prefixes:
                        h_prefix = f"h_{int(n):03d}"
                        h_cid = f"{h_prefix}{prefix}{num5}"
                        if h_cid not in possible_cids:
                            possible_cids.append(h_cid)
                else:
                    # 如果没有特定映射，尝试常见的h_前缀
                    try:
                        with open("h_prefix_numbers.json", "r", encoding="utf-8") as f:
                            h_prefix_numbers = json.load(f)
                        # 只尝试前几个最常见的h_前缀，避免太多请求
                        for n in h_prefix_numbers[:5]:  # 限制为前5个
                            h_prefix = f"h_{int(n):03d}"
                            h_cid = f"{h_prefix}{prefix}{num5}"
                            if h_cid not in possible_cids:
                                possible_cids.append(h_cid)
                    except Exception:
                        pass

            except Exception as e:
                print(f"⚠️ 读取CID映射配置时出错: {e}")

        return possible_cids

    def _is_code_match(self, input_normalized, result_normalized, original_input, original_result):
        """检查输入番号和结果是否匹配"""
        if not input_normalized or not result_normalized:
            return False

        # 直接匹配
        if input_normalized == result_normalized:
            return True

        # 检查是否是h_前缀的变体（如 ipx123 vs h_001ipx00123）
        if result_normalized.startswith("h_") and input_normalized in result_normalized:
            return True

        # 检查是否是数字补零的变体（如 ipx123 vs ipx00123）
        import re
        input_match = re.match(r"^([a-z]+)(\d+)$", input_normalized)
        result_match = re.match(r"^(?:h_\d+)?([a-z]+)(\d+)$", result_normalized)

        if input_match and result_match:
            input_prefix = input_match.group(1)
            input_number = int(input_match.group(2))
            result_prefix = result_match.group(1)
            result_number = int(result_match.group(2))

            # 前缀相同且数字相同（忽略前导零）
            if input_prefix == result_prefix and input_number == result_number:
                return True

        print(f"🔍 不匹配详情: {input_normalized} != {result_normalized}")
        return False

    def _load_json(self, path):
        """加载JSON文件"""
        try:
            with open(path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception:
            return []

    def _save_json(self, path, data):
        """保存JSON文件"""
        try:
            with open(path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return "保存成功"
        except Exception as e:
            return f"保存失败: {e}"

    def _save_dmm_data_to_json(self, dmm_result):
        """
        将DMM搜索结果保存到JSON配置文件
        完全复用原始gradio_app.py中的逻辑，保持数据结构的一致性
        """
        if not dmm_result or not dmm_result.get("success"):
            return {"success": False, "message": "无有效数据可保存"}

        try:
            code = dmm_result.get("code", "")
            cid = dmm_result.get("cid", "")

            if not code or not cid:
                return {"success": False, "message": "缺少必要的番号或CID信息"}

            print(f"开始保存数据: code={code}, cid={cid}")

            # 解析CID格式
            cid_lower = cid.lower()
            saved_items = []

            # 检查是否是h_prefix格式 (如: h_1462cawd00849)
            h_prefix_match = re.match(r"h_(\d{3,4})(.+)", cid_lower)
            if h_prefix_match:
                h_num = int(h_prefix_match.group(1))
                remaining_part = h_prefix_match.group(2)

                print(f"检测到h_prefix格式: h_num={h_num}, remaining={remaining_part}")

                # 更新 h_prefix_numbers.json
                try:
                    h_numbers = self._load_json("h_prefix_numbers.json")
                    if not isinstance(h_numbers, list):
                        h_numbers = []

                    if h_num not in h_numbers:
                        h_numbers.append(h_num)
                        h_numbers.sort()
                        save_result = self._save_json("h_prefix_numbers.json", h_numbers)
                        if "成功" in save_result:
                            saved_items.append(f"h_prefix_numbers.json: 添加 {h_num}")
                            print(f"成功添加h_prefix: {h_num}")
                        else:
                            print(f"保存h_prefix失败: {save_result}")
                    else:
                        print(f"h_prefix {h_num} 已存在")

                except Exception as e:
                    print(f"更新h_prefix_numbers.json失败: {e}")

                # 更新 prefix_h_prefix_map.json
                try:
                    # 提取实际前缀（去掉数字部分）
                    prefix_match = re.match(r"^([a-z]+)", remaining_part)
                    if prefix_match:
                        actual_prefix = prefix_match.group(1)

                        print(f"提取到实际前缀: {actual_prefix}")

                        try:
                            with open("prefix_h_prefix_map.json", "r", encoding="utf-8") as f:
                                prefix_h_map = json.load(f)
                        except:
                            prefix_h_map = {}

                        if actual_prefix not in prefix_h_map:
                            prefix_h_map[actual_prefix] = []

                        if h_num not in prefix_h_map[actual_prefix]:
                            prefix_h_map[actual_prefix].append(h_num)
                            prefix_h_map[actual_prefix].sort()
                            save_result = self._save_json("prefix_h_prefix_map.json", prefix_h_map)
                            if "成功" in save_result:
                                saved_items.append(f"prefix_h_prefix_map.json: {actual_prefix} -> {h_num}")
                                print(f"成功添加前缀映射: {actual_prefix} -> {h_num}")
                            else:
                                print(f"保存前缀映射失败: {save_result}")
                        else:
                            print(f"前缀映射 {actual_prefix} -> {h_num} 已存在")

                except Exception as e:
                    print(f"更新prefix_h_prefix_map.json失败: {e}")

            # 检查是否是数字前缀格式 (如: 1cawd00849, 140cawd00849)
            number_prefix_match = re.match(r"^(\d{1,4})([a-z]+)", cid_lower)
            if number_prefix_match:
                num_prefix = number_prefix_match.group(1)

                print(f"检测到数字前缀格式: num_prefix={num_prefix}")

                try:
                    number_prefixes = self._load_json("number_prefixes.json")
                    if not isinstance(number_prefixes, list):
                        number_prefixes = []

                    if num_prefix not in number_prefixes:
                        number_prefixes.append(num_prefix)
                        # 按数字大小排序
                        number_prefixes.sort(key=lambda x: int(x) if x.isdigit() else float('inf'))
                        save_result = self._save_json("number_prefixes.json", number_prefixes)
                        if "成功" in save_result:
                            saved_items.append(f"number_prefixes.json: 添加 {num_prefix}")
                            print(f"成功添加数字前缀: {num_prefix}")
                        else:
                            print(f"保存数字前缀失败: {save_result}")
                    else:
                        print(f"数字前缀 {num_prefix} 已存在")

                except Exception as e:
                    print(f"更新number_prefixes.json失败: {e}")

            # 检查是否是普通前缀格式，需要添加到基础前缀列表
            simple_prefix_match = re.match(r"^([a-z]+)\d+$", cid_lower)
            if simple_prefix_match and not h_prefix_match and not number_prefix_match:
                simple_prefix = simple_prefix_match.group(1)
                print(f"检测到普通前缀格式: simple_prefix={simple_prefix}")
                saved_items.append(f"标准格式CID: {cid_lower}")

            if saved_items:
                message = f"✅ 数据保存成功！\n  - " + "\n  - ".join(saved_items)
            else:
                message = f"✅ 数据保存成功！\n  - 标准格式CID: {cid_lower}"

            return {"success": True, "message": message}

        except Exception as e:
            error_msg = f"保存数据失败: {str(e)}"
            print(error_msg)
            return {"success": False, "message": error_msg}

    def get_poster_with_cid(self, cid, need_4k=False, need_subtitle=False):
        """
        直接使用CID获取海报图片
        """
        try:
            if not cid:
                return {
                    "success": False,
                    "poster_bytes": None,
                    "thumb_bytes": None,
                    "message": "CID为空，无法构建海报URL"
                }

            # 清理CID
            cid = str(cid).strip()
            print(f"🖼️ 准备获取海报，原始CID: '{cid}'")

            # 生成可能的CID格式列表，与dmm_tools的逻辑保持一致
            possible_cids = self._generate_poster_cid_list(cid)
            print(f"🖼️ 将尝试的CID格式: {possible_cids}")

            # 设置完整的请求头，与原版保持一致
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Referer": "https://www.dmm.co.jp/",
                "Accept": "image/webp,image/apng,image/*,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1"
            }

            poster_bytes = None
            thumb_bytes = None
            messages = []
            successful_cid = None
            successful_poster_url = None
            successful_thumb_url = None

            # 尝试所有可能的CID格式
            for try_cid in possible_cids:
                print(f"🖼️ 尝试CID格式: {try_cid}")

                # 构建URL
                thumb_url = f'https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{try_cid}/{try_cid}pl.jpg'
                poster_url = f'https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{try_cid}/{try_cid}ps.jpg'

                # 先尝试高清海报
                try:
                    poster_resp = requests.get(poster_url, headers=headers, timeout=10, allow_redirects=True)
                    print(f"🖼️ 海报请求状态 ({try_cid}): {poster_resp.status_code}")

                    if poster_resp.status_code == 200:
                        poster_bytes = poster_resp.content
                        successful_cid = try_cid
                        successful_poster_url = poster_url
                        successful_thumb_url = thumb_url
                        messages.append(f"✅ 高清海报获取成功 (CID: {try_cid}, {len(poster_bytes)} bytes)")
                        print(f"✅ 高清海报获取成功，CID: {try_cid}，大小: {len(poster_bytes)} bytes")

                        # 尝试获取对应的缩略图
                        try:
                            thumb_resp = requests.get(thumb_url, headers=headers, timeout=10, allow_redirects=True)
                            if thumb_resp.status_code == 200:
                                thumb_bytes = thumb_resp.content
                                messages.append(f"✅ 缩略图获取成功 ({len(thumb_bytes)} bytes)")
                                print(f"✅ 缩略图获取成功，大小: {len(thumb_bytes)} bytes")
                            else:
                                messages.append(f"⚠️ 缩略图获取失败，状态码: {thumb_resp.status_code}")
                        except Exception as e:
                            messages.append(f"⚠️ 缩略图请求异常: {str(e)}")

                        # 找到有效的海报就停止尝试
                        break

                except Exception as e:
                    print(f"🖼️ 海报请求异常 ({try_cid}): {str(e)}")
                    continue

            # 如果没有找到海报，记录失败信息
            if not poster_bytes:
                messages.append(f"❌ 尝试了 {len(possible_cids)} 种CID格式，均未找到有效海报")
                print(f"❌ 尝试了所有CID格式均失败: {possible_cids}")

            success = poster_bytes is not None or thumb_bytes is not None
            message = "\n".join(messages) if messages else "未获取到任何图片"

            print(f"🖼️ 海报获取完成，成功: {success}")

            return {
                "success": success,
                "poster_bytes": poster_bytes,
                "thumb_bytes": thumb_bytes,
                "message": message,
                "cid": successful_cid or cid,
                "poster_url": successful_poster_url or f'https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{cid}/{cid}ps.jpg',
                "thumb_url": successful_thumb_url or f'https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{cid}/{cid}pl.jpg'
            }

        except Exception as e:
            error_msg = f"海报获取异常: {str(e)}"
            print(f"❌ {error_msg}")
            traceback.print_exc()
            return {
                "success": False,
                "poster_bytes": None,
                "thumb_bytes": None,
                "message": error_msg
            }
    
    def get_poster_for_search(self, code, need_4k=False, need_subtitle=False):
        """
        为搜索功能获取海报图片
        使用DMM的海报URL构建机制
        """
        try:
            # 首先尝试从搜索结果中获取CID
            search_result = self.search_dmm_enhanced(code)
            if not search_result["success"]:
                return {
                    "success": False,
                    "poster_bytes": None,
                    "thumb_bytes": None,
                    "message": "无法获取CID，搜索失败"
                }

            cid = search_result["cid"]
            if not cid:
                return {
                    "success": False,
                    "poster_bytes": None,
                    "thumb_bytes": None,
                    "message": "CID为空，无法构建海报URL"
                }

            # 使用CID获取海报
            return self.get_poster_with_cid(cid, need_4k, need_subtitle)

        except Exception as e:
            error_msg = f"海报获取异常: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return {
                "success": False,
                "poster_bytes": None,
                "thumb_bytes": None,
                "message": error_msg
            }

    def parse_manual_dmm_url(self, url):
        """
        解析手动输入的DMM详情页URL
        提取CID和其他信息
        """
        try:
            # 从URL中提取CID
            cid_match = re.search(r'/cid=([^/?&]+)', url)
            if not cid_match:
                return {"success": False, "message": "无法从URL中提取CID"}

            cid = cid_match.group(1)
            print(f"从URL提取到CID: {cid}")

            # 尝试获取页面信息
            try:
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    "Cookie": "age_check_done=1; cklg=ja"
                }

                response = requests.get(url, headers=headers, timeout=10)
                if response.status_code == 200:
                    from bs4 import BeautifulSoup
                    soup = BeautifulSoup(response.text, "html.parser")

                    # 尝试提取厂牌信息
                    label_tag = soup.find("a", href=re.compile(r"/digital/videoa/-/list/=/article=maker/id="))
                    label = label_tag.text.strip() if label_tag else "未知厂牌"

                    return {
                        "success": True,
                        "cid": cid,
                        "label": label,
                        "url": url,
                        "message": f"✅ 成功解析URL，CID: {cid}, 厂牌: {label}"
                    }
                else:
                    return {
                        "success": True,
                        "cid": cid,
                        "label": "未知厂牌",
                        "url": url,
                        "message": f"✅ 提取到CID: {cid} (页面访问失败，状态码: {response.status_code})"
                    }

            except Exception as e:
                # 即使无法获取页面信息，也返回基本的CID信息
                return {
                    "success": True,
                    "cid": cid,
                    "label": "未知厂牌",
                    "url": url,
                    "message": f"✅ 提取到CID: {cid} (无法获取详细信息: {str(e)})"
                }

        except Exception as e:
            return {"success": False, "message": f"解析URL失败: {str(e)}"}

    def learn_from_successful_cid(self, cid):
        """
        从成功的CID中学习新的前缀规则
        """
        try:
            print(f"🎓 开始从成功的CID学习: {cid}")

            # 检测数字前缀格式 (如: 1240milk00251)
            # 这种格式实际上应该对应 h_1240milk00251
            number_prefix_match = re.match(r"^(\d{1,4})([a-z]+)", cid.lower())
            if number_prefix_match:
                number_part = number_prefix_match.group(1)
                letter_part = number_prefix_match.group(2)

                # 加载现有的映射
                h_prefix_file = "h_prefix_numbers.json"
                number_prefix_file = "number_prefixes.json"

                try:
                    with open(h_prefix_file, "r", encoding="utf-8") as f:
                        h_prefix_data = json.load(f)
                    # 确保是字典格式
                    if not isinstance(h_prefix_data, dict):
                        print(f"⚠️ {h_prefix_file} 格式错误，重置为字典格式")
                        h_prefix_data = {}
                except:
                    h_prefix_data = {}

                try:
                    with open(number_prefix_file, "r", encoding="utf-8") as f:
                        number_prefix_data = json.load(f)
                except:
                    number_prefix_data = {}

                # 检查是否需要添加新的映射
                h_prefix_key = f"h_{number_part}{letter_part}"

                if h_prefix_key not in h_prefix_data:
                    h_prefix_data[h_prefix_key] = number_part

                    with open(h_prefix_file, "w", encoding="utf-8") as f:
                        json.dump(h_prefix_data, f, ensure_ascii=False, indent=2)

                    print(f"✅ 已学习新的h_prefix映射: {h_prefix_key} -> {number_part}")
                    return f"已学习新的h_prefix映射: {h_prefix_key} -> {number_part}"

                # 检查number_prefixes映射
                if letter_part not in number_prefix_data:
                    number_prefix_data[letter_part] = number_part

                    with open(number_prefix_file, "w", encoding="utf-8") as f:
                        json.dump(number_prefix_data, f, ensure_ascii=False, indent=2)

                    print(f"✅ 已学习新的number_prefix映射: {letter_part} -> {number_part}")
                    return f"已学习新的number_prefix映射: {letter_part} -> {number_part}"

                return "该CID格式已存在于映射中，无需学习"

            return "该CID格式不需要学习新的映射规则"

        except Exception as e:
            error_msg = f"学习过程出错: {str(e)}"
            print(f"❌ {error_msg}")
            traceback.print_exc()
            return f"❌ {error_msg}"

    def save_dmm_data_to_json(self, dmm_result):
        """
        将DMM搜索结果保存到JSON配置文件
        完全复用dmm_tools.py中的逻辑，保持数据结构的一致性
        """
        if not dmm_result or not dmm_result.get("success"):
            return {"success": False, "message": "无有效数据可保存"}

        try:
            code = dmm_result.get("code", "")
            cid = dmm_result.get("cid", "")

            if not code or not cid:
                return {"success": False, "message": "缺少必要的番号或CID信息"}

            print(f"开始保存数据: code={code}, cid={cid}")

            # 解析CID格式
            cid_lower = cid.lower()
            saved_items = []

            # 检测数字前缀格式 (如: 1240milk00251)
            number_prefix_match = re.match(r"^(\d{1,4})([a-z]+)(\d+)$", cid_lower)
            if number_prefix_match:
                number_part = number_prefix_match.group(1)
                letter_part = number_prefix_match.group(2)
                num_part = number_prefix_match.group(3)

                # 保存到 h_prefix_numbers.json
                h_prefix_file = "h_prefix_numbers.json"
                try:
                    with open(h_prefix_file, "r", encoding="utf-8") as f:
                        h_prefix_data = json.load(f)
                    # 确保是字典格式
                    if not isinstance(h_prefix_data, dict):
                        print(f"⚠️ {h_prefix_file} 格式错误，重置为字典格式")
                        h_prefix_data = {}
                except:
                    h_prefix_data = {}

                h_prefix_key = f"h_{number_part}{letter_part}"
                if h_prefix_key not in h_prefix_data:
                    h_prefix_data[h_prefix_key] = number_part
                    with open(h_prefix_file, "w", encoding="utf-8") as f:
                        json.dump(h_prefix_data, f, ensure_ascii=False, indent=2)
                    saved_items.append(f"h_prefix_numbers.json: {h_prefix_key} -> {number_part}")

                # 保存到 number_prefixes.json
                number_prefix_file = "number_prefixes.json"
                try:
                    with open(number_prefix_file, "r", encoding="utf-8") as f:
                        number_prefix_data = json.load(f)
                    # 确保是字典格式
                    if not isinstance(number_prefix_data, dict):
                        print(f"⚠️ {number_prefix_file} 格式错误，重置为字典格式")
                        number_prefix_data = {}
                except:
                    number_prefix_data = {}

                if letter_part not in number_prefix_data:
                    number_prefix_data[letter_part] = number_part
                    with open(number_prefix_file, "w", encoding="utf-8") as f:
                        json.dump(number_prefix_data, f, ensure_ascii=False, indent=2)
                    saved_items.append(f"number_prefixes.json: {letter_part} -> {number_part}")

                # 保存到 prefix_h_prefix_map.json
                prefix_map_file = "prefix_h_prefix_map.json"
                try:
                    with open(prefix_map_file, "r", encoding="utf-8") as f:
                        prefix_map_data = json.load(f)
                except:
                    prefix_map_data = {}

                if letter_part not in prefix_map_data:
                    prefix_map_data[letter_part] = h_prefix_key
                    with open(prefix_map_file, "w", encoding="utf-8") as f:
                        json.dump(prefix_map_data, f, ensure_ascii=False, indent=2)
                    saved_items.append(f"prefix_h_prefix_map.json: {letter_part} -> {h_prefix_key}")

            # 检测标准格式 (如: abc00123)
            else:
                standard_match = re.match(r"^([a-z]+)(\d+)$", cid_lower)
                if standard_match:
                    letter_part = standard_match.group(1)
                    num_part = standard_match.group(2)

                    # 这种格式通常不需要特殊处理，但可以记录
                    saved_items.append(f"标准格式CID: {cid_lower}")

            if saved_items:
                message = f"✅ 数据保存成功！\n" + "\n".join([f"  - {item}" for item in saved_items])
                print(message)
                return {"success": True, "message": message}
            else:
                return {"success": True, "message": "✅ 数据已存在，无需重复保存"}

        except Exception as e:
            error_msg = f"保存数据时出错: {str(e)}"
            print(f"❌ {error_msg}")
            traceback.print_exc()
            return {"success": False, "message": error_msg}

    def auto_rename_files_for_code(self, code, target_dir, dmm_result):
        """
        根据搜索结果自动重命名指定目录下的对应文件
        复用批量重命名的逻辑
        """
        if not dmm_result or not dmm_result.get("success"):
            return {"success": False, "message": "无有效的DMM信息进行重命名"}

        if not os.path.exists(target_dir):
            return {"success": False, "message": f"目录不存在: {target_dir}"}

        try:
            # 查找匹配的文件
            matched_files = []
            base_code = code.upper().replace(" ", "").replace("-", "-")

            for root, _, files in os.walk(target_dir):
                for file in files:
                    if file.lower().endswith('.mp4'):
                        # 检查文件名是否包含番号
                        file_upper = file.upper()
                        if base_code in file_upper or code.upper() in file_upper:
                            matched_files.append(os.path.join(root, file))

            if not matched_files:
                return {"success": False, "message": f"在目录 {target_dir} 中未找到匹配番号 {code} 的文件"}

            # 重命名文件
            renamed_files = []
            cid = dmm_result.get("cid", "")

            for file_path in matched_files:
                try:
                    dir_name = os.path.dirname(file_path)
                    old_name = os.path.basename(file_path)

                    # 提取基础番号和后缀
                    match = re.match(r'^([A-Z0-9]+-\d+)(.*?)\.mp4$', old_name, re.I)
                    if match:
                        base_code = match.group(1)
                        suffix = match.group(2)

                        # 生成新文件名
                        if cid:
                            new_name = f"{base_code} ({cid}){suffix}.mp4"
                        else:
                            new_name = old_name  # 如果没有CID，保持原名

                        new_path = os.path.join(dir_name, new_name)

                        if file_path != new_path:
                            os.rename(file_path, new_path)
                            renamed_files.append(f"✅ {old_name} -> {new_name}")
                        else:
                            renamed_files.append(f"⏭️ {old_name} (无需重命名)")
                    else:
                        renamed_files.append(f"⚠️ {old_name} (格式不匹配，跳过)")

                except Exception as e:
                    renamed_files.append(f"❌ {old_name} (重命名失败: {str(e)})")

            return {
                "success": True,
                "message": f"处理完成，共处理 {len(matched_files)} 个文件",
                "details": renamed_files
            }

        except Exception as e:
            return {"success": False, "message": f"重命名过程出错: {str(e)}"}

    def get_image_info(self, image_bytes):
        """获取图片信息（尺寸、大小等）"""
        try:
            # 获取图片尺寸
            image = Image.open(io.BytesIO(image_bytes))
            width, height = image.size

            # 获取文件大小
            size_kb = len(image_bytes) / 1024
            size_mb = size_kb / 1024

            # 格式化大小
            if size_mb >= 1:
                size_str = f"{size_mb:.2f} MB"
            else:
                size_str = f"{size_kb:.2f} KB"

            return {
                "success": True,
                "width": width,
                "height": height,
                "size_bytes": len(image_bytes),
                "size_str": size_str,
                "format": image.format or "未知"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "width": 0,
                "height": 0,
                "size_bytes": 0,
                "size_str": "未知",
                "format": "未知"
            }

    def add_watermarks_to_image(self, image_bytes, need_4k=False, need_subtitle=False):
        """
        为图片添加水印
        """
        try:
            # 打开图片
            image = Image.open(io.BytesIO(image_bytes)).convert("RGBA")

            # 这里可以添加水印逻辑
            # 目前返回原图
            if need_4k or need_subtitle:
                # 可以在这里添加具体的水印处理逻辑
                pass

            # 转换回bytes
            output = io.BytesIO()
            image.convert("RGB").save(output, format="JPEG", quality=95)
            return output.getvalue()

        except Exception as e:
            print(f"添加水印失败: {str(e)}")
            return image_bytes  # 返回原图

    # FastDMM管理方法
    def get_fast_search_stats(self) -> dict:
        """获取FastDMM搜索引擎性能统计"""
        if self.enable_fast_search and self.fast_search:
            return self.fast_search.get_performance_stats()
        else:
            return {"error": "FastDMM搜索引擎不可用"}

    def build_fast_search_index(self, studios: list = None, number_range: tuple = (1, 300)) -> int:
        """构建FastDMM搜索索引"""
        if self.enable_fast_search and self.fast_search:
            if studios:
                # 为指定厂商构建索引
                total_built = 0
                for studio in studios:
                    built = self.fast_search.build_index_for_studio(studio, number_range)
                    total_built += built
                return total_built
            else:
                # 构建热门厂商索引
                return self.fast_search.build_popular_index(top_studios=10, number_range=number_range)
        else:
            print("❌ FastDMM搜索引擎不可用")
            return 0

    def cleanup_fast_search_database(self, days: int = 30) -> int:
        """清理FastDMM数据库"""
        if self.enable_fast_search and self.fast_search:
            return self.fast_search.cleanup_database(days)
        else:
            return 0

    def verify_fast_search_urls(self, count: int = 50) -> dict:
        """验证FastDMM索引中的URL"""
        if self.enable_fast_search and self.fast_search:
            return self.fast_search.verify_random_urls(count)
        else:
            return {"error": "FastDMM搜索引擎不可用"}

    def get_supported_studios(self) -> list:
        """获取FastDMM支持的厂商列表"""
        if self.enable_fast_search and self.fast_search:
            return self.fast_search.get_supported_studios()
        else:
            return []

    def is_studio_supported_by_fast_search(self, studio: str) -> bool:
        """检查厂商是否被FastDMM支持"""
        if self.enable_fast_search and self.fast_search:
            return self.fast_search.is_studio_supported(studio)
        else:
            return False

    def reset_fast_search_stats(self):
        """重置FastDMM性能统计"""
        if self.enable_fast_search and self.fast_search:
            self.fast_search.reset_stats()
            print("✅ FastDMM性能统计已重置")
        else:
            print("❌ FastDMM搜索引擎不可用")

    def toggle_fast_search(self, enable: bool):
        """启用/禁用FastDMM搜索引擎"""
        if enable and FAST_SEARCH_AVAILABLE:
            if not self.fast_search:
                self._init_fast_search()
            self.enable_fast_search = True
            print("✅ FastDMM搜索引擎已启用")
        else:
            self.enable_fast_search = False
            print("⚠️ FastDMM搜索引擎已禁用")

    # 智能搜索辅助方法
    def _format_multiple_results(self, search_response: dict, original_code: str) -> dict:
        """格式化多重映射结果"""
        formatted_results = []

        for result in search_response['results']:
            formatted_result = {
                "code": original_code,
                "cid": result.dmm_cid,
                "url": result.url,
                "label": result.studio,
                "confidence": result.confidence,
                "source": result.source,
                "mapping_type": getattr(result, 'mapping_type', 'unknown'),
                "mapping_priority": getattr(result, 'mapping_priority', 0),
                "is_primary": getattr(result, 'mapping_type', '') == 'primary'
            }
            formatted_results.append(formatted_result)

        return {
            "success": True,
            "search_type": "multiple",
            "message": f"找到 {len(formatted_results)} 个可能的映射结果",
            "results": formatted_results,
            "primary_result": next((r for r in formatted_results if r["is_primary"]), formatted_results[0]),
            "alternative_results": [r for r in formatted_results if not r["is_primary"]],
            "total_count": len(formatted_results)
        }



    def _format_single_result(self, result, original_code: str) -> dict:
        """格式化单一结果（保持原有格式）"""
        return {
            "success": True,
            "search_type": "single",
            "code": original_code,
            "cid": result.dmm_cid,
            "url": result.url,
            "label": result.studio,
            "confidence": getattr(result, 'confidence', 1.0),
            "source": getattr(result, 'source', 'intelligent_search'),
            "message": f"✅ 搜索成功！置信度: {getattr(result, 'confidence', 1.0):.2f}",
            "verified": getattr(result, 'verified', False)
        }

    def _try_fallback_search(self, code: str, error_message: str) -> dict:
        """尝试备用搜索"""
        print(f"⚠️ 智能搜索失败: {error_message}")
        print(f"🔄 尝试备用搜索...")

        try:
            fallback_result = self._fallback_dmm_search(code)
            if fallback_result and fallback_result.get("success"):
                fallback_result["message"] = f"✅ 备用搜索成功！原因: {error_message}"
                fallback_result["search_type"] = "fallback"
                return fallback_result
            else:
                return self._create_error_result(code, f"智能搜索和备用搜索都失败: {error_message}")
        except Exception as e:
            return self._create_error_result(code, f"备用搜索异常: {str(e)}")

    def _create_error_result(self, code: str, message: str) -> dict:
        """创建错误结果"""
        return {
            "success": False,
            "message": message,
            "code": code,
            "search_type": "failed"
        }

    # 向后兼容的函数
    def search_dmm(self, code):
        """保持向后兼容的原始搜索函数"""
        result = self.search_dmm_enhanced(code)
        if result["success"]:
            return f"番号: {result['code']}\nCID: {result['cid']}\n厂牌: {result['label']}\n详情页: {result['url']}"
        else:
            return result["message"]
