#!/usr/bin/env python3
"""
专门测试多重映射搜索功能
"""

import sys
import os
import sqlite3

# 添加模块路径
sys.path.append('modules')

from modules.search_detail import SearchDetailModule

def test_all_mappings():
    """测试所有映射的搜索功能"""
    print("🔀 多重映射搜索测试")
    print("=" * 60)

    search_detail = SearchDetailModule()
    
    # 测试番号
    test_queries = [
        "MILK-251",  # 已知有多重映射
        "CAWD-797",  # 单一映射测试
        "PRED-123",  # 单一映射测试
        "IPX-123"    # 另一个测试
    ]
    
    for query in test_queries:
        print(f"\n🧪 测试番号: {query}")
        print("=" * 40)
        
        # 1. 生成所有可能的CID
        possible_cids = search_detail._generate_possible_cids(query)
        print(f"🎯 生成的CID数量: {len(possible_cids)}")
        print(f"📋 CID列表: {possible_cids}")
        
        # 2. 测试每个CID
        results = []
        for i, cid in enumerate(possible_cids, 1):
            print(f"\n   🔍 测试 {i}/{len(possible_cids)}: {cid}")
            
            # 检查数据库
            db_result = check_database(cid)
            if db_result:
                print(f"   ✅ 数据库找到")
                print(f"      📝 标题: {db_result.get('title', 'N/A')}")
                print(f"      🎬 演员: {db_result.get('actress', 'N/A')}")
                results.append({
                    'cid': cid,
                    'found': True,
                    'source': 'database',
                    'title': db_result.get('title'),
                    'actress': db_result.get('actress')
                })
            else:
                print(f"   ❌ 数据库未找到")
                results.append({
                    'cid': cid,
                    'found': False,
                    'source': 'none',
                    'title': None,
                    'actress': None
                })
        
        # 3. 汇总结果
        print(f"\n📊 {query} 搜索结果汇总:")
        print("-" * 30)
        
        found_count = sum(1 for r in results if r['found'])
        print(f"✅ 找到结果: {found_count}/{len(results)}")
        
        if found_count > 0:
            print(f"📋 详细结果:")
            for i, result in enumerate(results, 1):
                if result['found']:
                    print(f"   {i}️⃣ CID: {result['cid']}")
                    print(f"      标题: {result['title'] or 'N/A'}")
                    print(f"      演员: {result['actress'] or 'N/A'}")
                    print(f"      来源: {result['source']}")
        else:
            print(f"❌ 所有映射都未找到结果")
            
        # 4. 测试完整搜索流程
        print(f"\n🔄 测试完整搜索流程:")
        full_result = search_detail.search_dmm_enhanced(query)
        if full_result and full_result.get("success"):
            print(f"✅ 完整搜索成功")
            print(f"   📍 返回CID: {full_result.get('cid', 'N/A')}")
            print(f"   📝 标题: {full_result.get('title', 'N/A')}")
            print(f"   📊 来源: {full_result.get('source', 'N/A')}")
        else:
            print(f"❌ 完整搜索失败")

def check_database(cid):
    """检查数据库中是否存在指定CID"""
    db_file = "dmm_firefox_database.db"
    
    if not os.path.exists(db_file):
        return None
        
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM dmm_works WHERE cid = ? LIMIT 1", (cid,))
        row = cursor.fetchone()
        
        if row:
            # 获取列名
            columns = [description[0] for description in cursor.description]
            result = dict(zip(columns, row))
            conn.close()
            return result
        else:
            conn.close()
            return None
            
    except Exception as e:
        print(f"      ⚠️ 数据库查询错误: {e}")
        return None

def test_specific_mappings():
    """测试特定的映射格式"""
    print("\n\n🎯 测试特定映射格式")
    print("=" * 60)
    
    # 已知的多重映射案例
    mapping_tests = [
        {
            'query': 'MILK-251',
            'expected_cids': ['h_1240milk00251', 'milk00251'],
            'description': 'MILK厂商的多重映射'
        }
    ]
    
    search_detail = SearchDetailModule()
    
    for test_case in mapping_tests:
        query = test_case['query']
        expected = test_case['expected_cids']
        desc = test_case['description']
        
        print(f"\n🧪 测试案例: {query}")
        print(f"📝 描述: {desc}")
        print(f"🎯 期望的CID: {expected}")
        
        # 生成实际的CID
        actual_cids = search_detail._generate_possible_cids(query)
        print(f"🔄 实际生成: {actual_cids}")
        
        # 检查是否包含期望的CID
        matches = []
        for expected_cid in expected:
            if expected_cid in actual_cids:
                matches.append(expected_cid)
                print(f"   ✅ 找到期望的CID: {expected_cid}")
            else:
                print(f"   ❌ 缺少期望的CID: {expected_cid}")
        
        print(f"📊 匹配率: {len(matches)}/{len(expected)} ({len(matches)/len(expected)*100:.1f}%)")

def main():
    """主测试函数"""
    print("🔀 多重映射搜索专项测试")
    print("=" * 60)
    
    try:
        test_all_mappings()
        test_specific_mappings()
        
        print("\n" + "=" * 60)
        print("✅ 多重映射测试完成！")
        print("💡 建议: 根据测试结果优化映射策略")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
