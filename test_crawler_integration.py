#!/usr/bin/env python3
"""
测试爬虫集成功能
"""

import os
import sys
from mma_dmm_integration import MMADMMIntegration

def test_basic_search():
    """测试基础搜索功能"""
    print("🧪 测试基础搜索功能")
    print("=" * 50)
    
    # 创建集成服务（启用实时爬取）
    integration = MMADMMIntegration(enable_realtime_crawl=True)
    
    # 测试查询列表
    test_queries = [
        "CAWD-797",  # 标准番号
        "MILK-251",  # 另一个番号
        "cawd00797", # CID格式
    ]
    
    for query in test_queries:
        print(f"\n🔍 搜索: {query}")
        print("-" * 30)
        
        # 测试普通搜索（可能触发爬虫）
        result = integration.search_work_info(query, enable_crawl=True)
        
        if result and result.get("success"):
            print(f"✅ 搜索成功")
            print(f"📍 CID: {result.get('cid', 'N/A')}")
            print(f"📝 标题: {result.get('title', 'N/A')}")
            print(f"🎬 演员: {result.get('actress', 'N/A')}")
            print(f"⏱️ 时长: {result.get('duration', 'N/A')}")
            print(f"📊 来源: {result.get('source', 'N/A')}")
            print(f"💬 消息: {result.get('message', 'N/A')}")
        else:
            print(f"❌ 搜索失败")
            print(f"💬 消息: {result.get('message', 'N/A') if result else '无结果'}")

def test_manual_search():
    """测试手动搜索（强制爬取）"""
    print("\n\n🔍 测试手动搜索（强制爬取）")
    print("=" * 50)
    
    integration = MMADMMIntegration(enable_realtime_crawl=True)
    
    # 选择一个测试番号
    test_query = "CAWD-797"
    
    print(f"🕷️ 手动搜索（强制爬取）: {test_query}")
    print("-" * 30)
    
    result = integration.manual_search_with_crawl(test_query)
    
    if result and result.get("success"):
        print(f"✅ 手动搜索成功")
        print(f"📍 CID: {result.get('cid', 'N/A')}")
        print(f"📝 标题: {result.get('title', 'N/A')}")
        print(f"🎬 演员: {result.get('actress', 'N/A')}")
        print(f"🏭 厂商: {result.get('studio', 'N/A')}")
        print(f"📅 发售日: {result.get('release_date', 'N/A')}")
        print(f"🎭 类型: {result.get('genre', 'N/A')}")
        print(f"⭐ 评分: {result.get('rating', 'N/A')}")
        print(f"📊 来源: {result.get('source', 'N/A')}")
    else:
        print(f"❌ 手动搜索失败")
        print(f"💬 消息: {result.get('message', 'N/A') if result else '无结果'}")

def test_batch_search():
    """测试批量搜索"""
    print("\n\n📦 测试批量搜索")
    print("=" * 50)
    
    integration = MMADMMIntegration(enable_realtime_crawl=True)
    
    # 批量查询列表
    batch_queries = [
        "CAWD-797",
        "MILK-251", 
        "UNKNOWN-999"  # 不存在的番号
    ]
    
    print(f"🔍 批量搜索 {len(batch_queries)} 个番号")
    print(f"📋 查询列表: {', '.join(batch_queries)}")
    print("-" * 30)
    
    # 限制爬取数量避免被封
    results = integration.batch_search_works(batch_queries, enable_crawl=True, max_crawl=2)
    
    for query, result in results.items():
        print(f"\n📄 {query}:")
        if result and result.get("success"):
            print(f"  ✅ 找到: {result.get('title', 'N/A')}")
            print(f"  📍 CID: {result.get('cid', 'N/A')}")
            print(f"  📊 来源: {result.get('source', 'N/A')}")
        else:
            print(f"  ❌ 未找到: {result.get('message', 'N/A') if result else '无结果'}")

def test_database_status():
    """测试数据库状态"""
    print("\n\n📊 测试数据库状态")
    print("=" * 50)
    
    integration = MMADMMIntegration(enable_realtime_crawl=True)
    
    # 健康检查
    health = integration.health_check()
    print(f"🏥 系统状态: {health['status']}")
    
    # 统计信息
    stats = integration.get_statistics()
    print(f"\n📈 数据库统计:")
    
    for db_name, db_stats in stats.items():
        print(f"  📋 {db_name}:")
        if isinstance(db_stats, dict) and "error" not in db_stats:
            for key, value in db_stats.items():
                print(f"    {key}: {value}")
        else:
            print(f"    状态: {db_stats}")

def test_rename_integration():
    """测试重命名集成"""
    print("\n\n📝 测试重命名集成")
    print("=" * 50)
    
    integration = MMADMMIntegration(enable_realtime_crawl=True)
    
    # 模拟文件列表
    test_files = [
        "CAWD-797.mp4",
        "CAWD-797-4K.mp4",
        "MILK-251-C.mp4",
        "unknown-file.mp4"
    ]
    
    print(f"📁 测试文件列表:")
    for file in test_files:
        print(f"  📄 {file}")
    
    print(f"\n🔍 预览重命名结果:")
    print("-" * 30)
    
    # 预览重命名
    preview = integration.preview_rename(test_files)
    
    for result in preview:
        print(f"📄 {result['original']}")
        print(f"  → {result['new']}")
        print(f"  状态: {result['status']}")
        print(f"  原因: {result['reason']}")
        if result.get('number'):
            print(f"  番号: {result['number']}")
        if result.get('cid'):
            print(f"  CID: {result['cid']}")
        if result.get('source'):
            print(f"  来源: {result['source']}")
        print()

def main():
    """主测试函数"""
    print("🚀 MMA-DMM爬虫集成测试")
    print("=" * 60)
    
    # 检查依赖
    try:
        import requests
        import bs4
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请安装: pip install requests beautifulsoup4")
        return
    
    # 检查数据库文件
    db_file = "dmm_firefox_database.db"
    if os.path.exists(db_file):
        print(f"✅ 数据库文件存在: {db_file}")
    else:
        print(f"⚠️ 数据库文件不存在: {db_file}")
        print("将在首次爬取时自动创建")
    
    print("\n" + "=" * 60)
    
    try:
        # 运行测试
        test_database_status()
        test_basic_search()
        test_manual_search()
        test_batch_search()
        test_rename_integration()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
