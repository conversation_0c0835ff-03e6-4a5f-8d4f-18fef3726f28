#!/usr/bin/env python3
"""
MMA-DMM爬虫集成使用示例
"""

from mma_dmm_integration import MMADMMIntegration

# 创建集成服务
integration = MMADMMIntegration(enable_realtime_crawl=True)

# ==================== 搜索功能示例 ====================

# 1. 普通搜索（智能模式）
print("🔍 普通搜索示例:")
result = integration.search_work_info("CAWD-797")
if result['success']:
    print(f"标题: {result['title']}")
    print(f"演员: {result['actress']}")
    print(f"来源: {result['source']}")  # local_database 或 realtime_crawl

# 2. 手动搜索（强制爬取）
print("\n🕷️ 手动搜索示例:")
result = integration.manual_search_with_crawl("MILK-251")
if result['success']:
    print(f"标题: {result['title']}")
    print(f"类型: {result['genre']}")
    print(f"评分: {result['rating']}")

# 3. 批量搜索
print("\n📦 批量搜索示例:")
queries = ["CAWD-797", "MILK-251", "PRED-123"]
results = integration.batch_search_works(queries, max_crawl=5)
for query, result in results.items():
    status = "✅" if result['success'] else "❌"
    print(f"{status} {query}: {result.get('title', '未找到')}")

# ==================== 重命名功能示例 ====================

# 4. 文件重命名预览
print("\n📝 重命名预览示例:")
test_files = ["CAWD-797.mp4", "MILK-251-4K.mp4"]
preview = integration.preview_rename(test_files)
for item in preview:
    print(f"{item['original']} → {item['new']}")

# 5. 目录批量重命名
print("\n📁 目录重命名示例:")
# result = integration.rename_directory("./videos", dry_run=True)
# print(f"找到 {result['summary']['total']} 个文件")

# ==================== 系统状态示例 ====================

# 6. 健康检查
print("\n🏥 系统状态:")
health = integration.health_check()
print(f"状态: {health['status']}")

# 7. 数据库统计
print("\n📊 数据库统计:")
stats = integration.get_statistics()
for db_name, db_stats in stats.items():
    if 'total_works' in db_stats:
        print(f"{db_name}: {db_stats['total_works']} 作品")
